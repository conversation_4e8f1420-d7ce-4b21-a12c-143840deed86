/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import React from 'react';
import { Box, Text } from 'ink';
import { Colors } from '../colors.js';
import { ApprovalMode } from '@arien/arien-cli-core';

interface AutoAcceptIndicatorProps {
  approvalMode: ApprovalMode;
}

export const AutoAcceptIndicator: React.FC<AutoAcceptIndicatorProps> = ({
  approvalMode,
}) => {
  let textColor = '';
  let icon = '';
  let textContent = '';
  let subText = '';

  switch (approvalMode) {
    case ApprovalMode.AUTO_EDIT:
      textColor = Colors.AccentGreen;
      icon = '◉';
      textContent = 'Auto-Accept Edits';
      subText = ' (Shift+Tab)';
      break;
    case ApprovalMode.YOLO:
      textColor = Colors.AccentRed;
      icon = '⚡';
      textContent = 'YOLO Mode';
      subText = ' (Ctrl+Y)';
      break;
    case ApprovalMode.DEFAULT:
    default:
      break;
  }

  return (
    <Box borderStyle="round" borderColor={textColor} paddingX={1}>
      <Text color={textColor} bold>
        {icon} {textContent}
      </Text>
      {subText && <Text color={Colors.Gray} dimColor>{subText}</Text>}
    </Box>
  );
};
