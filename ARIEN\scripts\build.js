/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */

import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.dirname(__dirname);

/**
 * Build all modules in the correct order
 */
async function buildAll() {
  console.log('🚀 Building ARIEN AI CLI...');
  
  const modules = ['core', 'cli'];
  
  for (const module of modules) {
    console.log(`\n📦 Building module: ${module}`);
    
    const moduleDir = path.join(rootDir, 'modules', module);
    const buildProcess = spawn('npm', ['run', 'build'], {
      stdio: 'inherit',
      shell: true,
      cwd: moduleDir,
    });
    
    await new Promise((resolve, reject) => {
      buildProcess.on('close', (code) => {
        if (code === 0) {
          console.log(`✅ Module ${module} built successfully`);
          resolve();
        } else {
          console.error(`❌ Module ${module} build failed with code ${code}`);
          reject(new Error(`Build failed for module ${module} with code ${code}`));
        }
      });
    });
  }
  
  console.log('\n🎉 All modules built successfully!');
}

// Run the build
buildAll().catch((error) => {
  console.error('Build failed:', error);
  process.exit(1);
});
