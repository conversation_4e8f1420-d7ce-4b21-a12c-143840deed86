/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */
export class ArienError extends Error {
    code;
    details;
    constructor(message, code, details) {
        super(message);
        this.name = 'ArienError';
        this.code = code;
        this.details = details;
    }
}
export class ConfigurationError extends ArienError {
    constructor(message, details) {
        super(message, 'CONFIGURATION_ERROR', details);
        this.name = 'ConfigurationError';
    }
}
export class AuthenticationError extends ArienError {
    constructor(message, details) {
        super(message, 'AUTHENTICATION_ERROR', details);
        this.name = 'AuthenticationError';
    }
}
export class ApiError extends ArienError {
    statusCode;
    constructor(message, statusCode, details) {
        super(message, 'API_ERROR', details);
        this.name = 'ApiError';
        this.statusCode = statusCode;
    }
}
export class ToolExecutionError extends ArienError {
    toolName;
    constructor(message, toolName, details) {
        super(message, 'TOOL_EXECUTION_ERROR', details);
        this.name = 'ToolExecutionError';
        this.toolName = toolName;
    }
}
export class ValidationError extends ArienError {
    field;
    constructor(message, field, details) {
        super(message, 'VALIDATION_ERROR', details);
        this.name = 'ValidationError';
        this.field = field;
    }
}
export class FileSystemError extends ArienError {
    path;
    constructor(message, path, details) {
        super(message, 'FILESYSTEM_ERROR', details);
        this.name = 'FileSystemError';
        this.path = path;
    }
}
export class NetworkError extends ArienError {
    url;
    constructor(message, url, details) {
        super(message, 'NETWORK_ERROR', details);
        this.name = 'NetworkError';
        this.url = url;
    }
}
export class SandboxError extends ArienError {
    constructor(message, details) {
        super(message, 'SANDBOX_ERROR', details);
        this.name = 'SandboxError';
    }
}
export class TimeoutError extends ArienError {
    timeoutMs;
    constructor(message, timeoutMs) {
        super(message, 'TIMEOUT_ERROR', { timeoutMs });
        this.name = 'TimeoutError';
        this.timeoutMs = timeoutMs;
    }
}
/**
 * Utility function to check if an error is of a specific type
 */
export function isArienError(error) {
    return error instanceof ArienError;
}
/**
 * Utility function to extract error message from any error type
 */
export function getErrorMessage(error) {
    if (error instanceof Error) {
        return error.message;
    }
    if (typeof error === 'string') {
        return error;
    }
    return 'Unknown error occurred';
}
/**
 * Utility function to extract error details for logging
 */
export function getErrorDetails(error) {
    if (isArienError(error)) {
        return {
            name: error.name,
            code: error.code,
            message: error.message,
            details: error.details,
            stack: error.stack,
        };
    }
    if (error instanceof Error) {
        return {
            name: error.name,
            message: error.message,
            stack: error.stack,
        };
    }
    return {
        error: String(error),
    };
}
/**
 * Utility function to create a standardized error response
 */
export function createErrorResponse(error) {
    const message = getErrorMessage(error);
    const code = isArienError(error) ? error.code : undefined;
    const details = isArienError(error) ? error.details : undefined;
    return {
        success: false,
        error: {
            message,
            code,
            details,
        },
    };
}
