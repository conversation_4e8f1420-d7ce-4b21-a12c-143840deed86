/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import React from 'react';
import { Box, Text } from 'ink';
import { Colors } from '../../colors.js';

export type StatusType = 'success' | 'error' | 'warning' | 'info' | 'pending' | 'custom';

interface StatusBadgeProps {
  status: StatusType;
  text: string;
  customColor?: string;
  customIcon?: string;
  compact?: boolean;
}

const statusConfig = {
  success: { icon: '✓', color: Colors.AccentGreen },
  error: { icon: '✗', color: Colors.AccentRed },
  warning: { icon: '⚠', color: Colors.AccentYellow },
  info: { icon: 'i', color: Colors.AccentCyan },
  pending: { icon: '◎', color: Colors.AccentPurple },
  custom: { icon: '●', color: Colors.Gray },
};

export const StatusBadge: React.FC<StatusBadgeProps> = ({
  status,
  text,
  customColor,
  customIcon,
  compact = false,
}) => {
  const config = statusConfig[status] || statusConfig.custom;
  const icon = customIcon || config.icon;
  const color = customColor || config.color;

  if (compact) {
    return (
      <Text color={color} bold>
        {icon} {text}
      </Text>
    );
  }

  return (
    <Box borderStyle="round" borderColor={color} paddingX={1}>
      <Text color={color} bold>
        {icon} {text}
      </Text>
    </Box>
  );
};

interface StatusListProps {
  items: Array<{
    status: StatusType;
    text: string;
    customColor?: string;
    customIcon?: string;
  }>;
  title?: string;
}

export const StatusList: React.FC<StatusListProps> = ({ items, title }) => {
  if (items.length === 0) return null;

  return (
    <Box flexDirection="column" marginTop={1}>
      {title && (
        <Text color={Colors.AccentCyan} bold>
          {title}
        </Text>
      )}
      {items.map((item, index) => (
        <Box key={index} marginLeft={title ? 2 : 0}>
          <StatusBadge {...item} compact />
        </Box>
      ))}
    </Box>
  );
}; 