/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import React from 'react';
import { Text, Box } from 'ink';
import { Colors } from '../../colors.js';

interface InfoMessageProps {
  text: string;
}

export const InfoMessage: React.FC<InfoMessageProps> = ({ text }) => {
  const prefix = '';
  const prefixWidth = prefix.length;

  // Detect important info patterns
  const isImportant = text.toLowerCase().includes('success') || 
                     text.toLowerCase().includes('complete') ||
                     text.toLowerCase().includes('refresh');
  
  // Choose appropriate color based on content
  const textColor = isImportant ? Colors.AccentGreen : Colors.Foreground;
  const prefixColor = isImportant ? Colors.AccentGreen : Colors.AccentCyan;

  return (
    <Box flexDirection="row" marginTop={1} marginBottom={0}>
      <Box width={prefixWidth}>
        <Text color={prefixColor} bold>{prefix}</Text>
      </Box>
      <Box flexGrow={1}>
        <Text wrap="wrap" color={textColor}>
          {text}
        </Text>
      </Box>
    </Box>
  );
};
