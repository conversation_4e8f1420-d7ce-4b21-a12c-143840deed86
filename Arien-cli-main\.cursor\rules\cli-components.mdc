---
description: 
globs: 
alwaysApply: true
---
# CLI Package Components Guide

## React Terminal Interface

### Main Application Components
- **App Component**: [packages/cli/src/ui/App.tsx](mdc:packages/cli/src/ui/App.tsx) - Root React component for terminal UI
- **Main CLI Logic**: [packages/cli/src/arien.tsx](mdc:packages/cli/src/arien.tsx) - Primary CLI application orchestration
- **Non-Interactive Mode**: [packages/cli/src/nonInteractiveCli.ts](mdc:packages/cli/src/nonInteractiveCli.ts) - Batch processing without UI

### UI Component System

#### Message Components
- **Arien Messages**: [packages/cli/src/ui/components/messages/ArienMessage.tsx](mdc:packages/cli/src/ui/components/messages/ArienMessage.tsx) - AI response rendering
- **User Messages**: [packages/cli/src/ui/components/messages/UserMessage.tsx](mdc:packages/cli/src/ui/components/messages/UserMessage.tsx) - User input display
- **Tool Messages**: [packages/cli/src/ui/components/messages/ToolMessage.tsx](mdc:packages/cli/src/ui/components/messages/ToolMessage.tsx) - Tool execution results

#### Interface Components
- **Input Handling**: [packages/cli/src/ui/components/UserInputBox.tsx](mdc:packages/cli/src/ui/components/UserInputBox.tsx) - Terminal input interface
- **Progress Indicators**: [packages/cli/src/ui/components/ProgressIndicator.tsx](mdc:packages/cli/src/ui/components/ProgressIndicator.tsx) - Loading states
- **Error Display**: [packages/cli/src/ui/components/ErrorDisplay.tsx](mdc:packages/cli/src/ui/components/ErrorDisplay.tsx) - Error presentation

### Shared Components
- **Layout Components**: [packages/cli/src/ui/components/shared/](mdc:packages/cli/src/ui/components/shared) - Reusable UI elements
- **Max Sized Box**: [packages/cli/src/ui/components/shared/MaxSizedBox.tsx](mdc:packages/cli/src/ui/components/shared/MaxSizedBox.tsx) - Terminal layout helper

## Theme System

### Theme Configuration
- **Theme Manager**: [packages/cli/src/ui/themes/](mdc:packages/cli/src/ui/themes) - Color theme management
- **Default Themes**: Multiple built-in themes (ansi, dracula, github, etc.)
- **Theme Selection**: [docs/cli/themes.md](mdc:docs/cli/themes.md) - Theme customization guide

### Available Themes
- **ANSI**: [packages/cli/src/ui/themes/ansi.ts](mdc:packages/cli/src/ui/themes/ansi.ts) - Terminal ANSI colors
- **Dracula**: [packages/cli/src/ui/themes/dracula.ts](mdc:packages/cli/src/ui/themes/dracula.ts) - Dark theme
- **GitHub**: [packages/cli/src/ui/themes/github.ts](mdc:packages/cli/src/ui/themes/github.ts) - GitHub-style theme
- **Light Variants**: Multiple light theme options

## React Hooks & State Management

### Custom Hooks
- **Command Processing**: [packages/cli/src/ui/hooks/atCommandProcessor.ts](mdc:packages/cli/src/ui/hooks/atCommandProcessor.ts) - @ command handling
- **Input Hooks**: [packages/cli/src/ui/hooks/useInput.ts](mdc:packages/cli/src/ui/hooks/useInput.ts) - Terminal input management
- **State Hooks**: Various hooks for managing CLI state

### Context Providers
- **Overflow Context**: [packages/cli/src/ui/contexts/OverflowContext.tsx](mdc:packages/cli/src/ui/contexts/OverflowContext.tsx) - Terminal overflow handling
- **Theme Context**: Theme state management
- **Configuration Context**: CLI settings context

## Configuration Management

### CLI Configuration
- **Main Config**: [packages/cli/src/config/config.ts](mdc:packages/cli/src/config/config.ts) - CLI settings management
- **Settings**: [packages/cli/src/config/settings.ts](mdc:packages/cli/src/config/settings.ts) - User preferences
- **Auth Config**: [packages/cli/src/config/auth.ts](mdc:packages/cli/src/config/auth.ts) - Authentication handling

### Extension Integration
- **Extension Config**: [packages/cli/src/config/extension.ts](mdc:packages/cli/src/config/extension.ts) - IDE extension integration
- **Editor Settings**: [packages/cli/src/ui/editors/editorSettingsManager.ts](mdc:packages/cli/src/ui/editors/editorSettingsManager.ts) - Editor preferences

## Terminal Interface Features

### Input Processing
- **Command Parsing**: Support for @ commands and special input
- **History Management**: Command history and navigation
- **Tab Completion**: Smart completion for files and commands

### Output Rendering
- **Code Highlighting**: [packages/cli/src/ui/utils/CodeColorizer.tsx](mdc:packages/cli/src/ui/utils/CodeColorizer.tsx) - Syntax highlighting
- **Markdown Rendering**: Rich markdown display in terminal
- **Progress Tracking**: Real-time progress indicators

### Privacy & Terms
- **Privacy Notice**: [packages/cli/src/ui/privacy/ArienPrivacyNotice.tsx](mdc:packages/cli/src/ui/privacy/ArienPrivacyNotice.tsx) - Privacy information display
- **Terms Display**: User agreement presentation

## Utilities & Helpers

### CLI Utilities
- **Cleanup**: [packages/cli/src/utils/cleanup.ts](mdc:packages/cli/src/utils/cleanup.ts) - Resource cleanup
- **Version**: [packages/cli/src/utils/version.ts](mdc:packages/cli/src/utils/version.ts) - Version information
- **Startup Warnings**: [packages/cli/src/utils/startupWarnings.ts](mdc:packages/cli/src/utils/startupWarnings.ts) - Initial setup warnings

### Input/Output
- **STDIN Reading**: [packages/cli/src/utils/readStdin.ts](mdc:packages/cli/src/utils/readStdin.ts) - Standard input handling
- **Sandbox Utils**: [packages/cli/src/utils/sandbox.ts](mdc:packages/cli/src/utils/sandbox.ts) - Sandboxing utilities

