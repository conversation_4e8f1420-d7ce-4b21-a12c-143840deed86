/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import React from 'react';
import { Box, Text } from 'ink';
import { Colors } from '../../colors.js';

interface SeparatorProps {
  width?: number;
  character?: string;
  color?: string;
  dimColor?: boolean;
  marginTop?: number;
  marginBottom?: number;
}

export const Separator: React.FC<SeparatorProps> = ({
  width = 50,
  character = '─',
  color = Colors.Gray,
  dimColor = true,
  marginTop = 0,
  marginBottom = 0,
}) => (
    <Box marginTop={marginTop} marginBottom={marginBottom}>
      <Text color={color} dimColor={dimColor}>
        {character.repeat(width)}
      </Text>
    </Box>
  );

interface DottedSeparatorProps {
  width?: number;
  color?: string;
  marginTop?: number;
  marginBottom?: number;
}

export const DottedSeparator: React.FC<DottedSeparatorProps> = ({
  width = 50,
  color = Colors.Gray,
  marginTop = 0,
  marginBottom = 0,
}) => {
  const pattern = '· ';
  const count = Math.floor(width / pattern.length);
  
  return (
    <Box marginTop={marginTop} marginBottom={marginBottom}>
      <Text color={color} dimColor>
        {pattern.repeat(count)}
      </Text>
    </Box>
  );
};

interface SectionSeparatorProps {
  title?: string;
  width?: number;
  color?: string;
  marginTop?: number;
  marginBottom?: number;
}

export const SectionSeparator: React.FC<SectionSeparatorProps> = ({
  title,
  width = 50,
  color = Colors.AccentCyan,
  marginTop = 1,
  marginBottom = 1,
}) => {
  if (!title) {
    return <Separator width={width} color={color} marginTop={marginTop} marginBottom={marginBottom} />;
  }

  const titleWithSpaces = ` ${title} `;
  const titleWidth = titleWithSpaces.length;
  const sideWidth = Math.max(1, Math.floor((width - titleWidth) / 2));
  
  return (
    <Box marginTop={marginTop} marginBottom={marginBottom}>
      <Text color={color}>
        {'─'.repeat(sideWidth)}
        <Text bold>{titleWithSpaces}</Text>
        {'─'.repeat(sideWidth)}
      </Text>
    </Box>
  );
}; 