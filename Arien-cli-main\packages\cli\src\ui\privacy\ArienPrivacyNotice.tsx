 /**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { Box, Newline, Text, useInput } from 'ink';
import { Colors } from '../colors.js';

interface ArienPrivacyNoticeProps {
  onExit: () => void;
}

export const ArienPrivacyNotice = ({ onExit }: ArienPrivacyNoticeProps) => {
  useInput((input, key) => {
    if (key.escape) {
      onExit();
    }
  });

  return (
    <Box flexDirection="column" marginBottom={1}>
      <Text bold color={Colors.AccentPurple}>
        Arien AI API Key Notice
      </Text>
      <Newline />
      <Text>
        By using the Arien AI API<Text color={Colors.AccentBlue}>[1]</Text>,
        Arien AI Studio
        <Text color={Colors.AccentRed}>[2]</Text>, and the other Arien AI
        developer services that reference these terms (collectively, the
        &quot;APIs&quot; or &quot;Services&quot;), you are agreeing to Arien AI
        APIs Terms of Service (the &quot;API Terms&quot;)
        <Text color={Colors.AccentGreen}>[3]</Text>, and the Arien AI
        Additional Terms of Service (the &quot;Additional Terms&quot;)
        <Text color={Colors.AccentPurple}>[4]</Text>.
      </Text>
      <Newline />
      <Text>
        <Text color={Colors.AccentBlue}>[1]</Text>{' '}
        https://arien.ai/docs/api_overview
      </Text>
      <Text>
        <Text color={Colors.AccentRed}>[2]</Text> https://studio.arien.ai/
      </Text>
      <Text>
        <Text color={Colors.AccentGreen}>[3]</Text>{' '}
        https://arien.ai/terms
      </Text>
      <Text>
        <Text color={Colors.AccentPurple}>[4]</Text>{' '}
        https://arien.ai/api/terms
      </Text>
      <Newline />
      <Text color={Colors.Gray}>Press Esc to exit.</Text>
    </Box>
  );
};