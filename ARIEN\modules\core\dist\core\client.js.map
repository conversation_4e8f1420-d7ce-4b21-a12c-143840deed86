{"version": 3, "file": "client.js", "sourceRoot": "", "sources": ["../../src/core/client.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,kBAAkB,EAAqC,MAAM,uBAAuB,CAAC;AAE9F,OAAO,EAAE,cAAc,EAAE,MAAM,qBAAqB,CAAC;AACrD,OAAO,EAAE,mBAAmB,EAAE,QAAQ,EAAE,MAAM,oBAAoB,CAAC;AACnE,OAAO,EAAE,gBAAgB,EAAE,MAAM,mBAAmB,CAAC;AAuCrD,MAAM,OAAO,WAAW;IACd,KAAK,CAAqB;IAC1B,KAAK,CAAkB;IACvB,MAAM,CAAS;IACf,WAAW,CAAoC;IAEvD,YAAY,MAAc;QACxB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QAErB,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAChC,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,mBAAmB,CAAC,0FAA0F,CAAC,CAAC;QAC5H,CAAC;QAED,IAAI,CAAC,KAAK,GAAG,IAAI,kBAAkB,CAAC,MAAM,CAAC,CAAC;QAC5C,IAAI,CAAC,WAAW,GAAG,cAAc,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;QAErD,kDAAkD;QAClD,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC;YACzC,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI;YAC5B,gBAAgB,EAAE,IAAI,CAAC,mBAAmB,EAAE;SAC7C,CAAC,CAAC;IACL,CAAC;IAEO,SAAS;QACf,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;QAE3C,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,SAAS;gBACZ,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,IAAI,EAAE,CAAC;YACvC,KAAK,iBAAiB;gBACpB,iDAAiD;gBACjD,MAAM,IAAI,mBAAmB,CAAC,oDAAoD,CAAC,CAAC;YACtF,KAAK,OAAO;gBACV,uCAAuC;gBACvC,MAAM,IAAI,mBAAmB,CAAC,0CAA0C,CAAC,CAAC;YAC5E;gBACE,MAAM,IAAI,mBAAmB,CAAC,gCAAgC,QAAQ,EAAE,CAAC,CAAC;QAC9E,CAAC;IACH,CAAC;IAEO,mBAAmB;QACzB,OAAO;YACL,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE;YACzC,eAAe,EAAE,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE;YAC3C,IAAI,EAAE,IAAI;YACV,IAAI,EAAE,EAAE;SACT,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe,CACnB,MAAc,EACd,UAAuB,EAAE;QAEzB,IAAI,CAAC;YACH,MAAM,gBAAgB,GAAG;gBACvB,GAAG,IAAI,CAAC,mBAAmB,EAAE;gBAC7B,GAAG,OAAO;aACX,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,gBAAgB,CAAC,KAAK,IAAI,EAAE;gBAC/C,OAAO,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC;oBAChC,QAAQ,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC;oBACvD,gBAAgB;iBACjB,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;YACjC,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;YAE7B,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,MAAM,IAAI,QAAQ,CAAC,8BAA8B,CAAC,CAAC;YACrD,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBAC3B,MAAM,IAAI,QAAQ,CAAC,yBAAyB,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAC/D,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CACb,UAAyB,EAAE,EAC3B,QAA0B,EAAE;QAE5B,MAAM,cAAc,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC;YACrC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC;gBAC5B,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI;gBAC5B,gBAAgB,EAAE,IAAI,CAAC,mBAAmB,EAAE;gBAC5C,KAAK,EAAE,CAAC,EAAE,oBAAoB,EAAE,KAAK,EAAE,CAAC;aACzC,CAAC;YACJ,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC;QAEf,MAAM,IAAI,GAAG,cAAc,CAAC,SAAS,CAAC;YACpC,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;gBAC3B,IAAI,EAAE,GAAG,CAAC,IAAI;gBACd,KAAK,EAAE,GAAG,CAAC,KAAK;aACjB,CAAC,CAAC;SACJ,CAAC,CAAC;QAEH,OAAO,IAAI,gBAAgB,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;IACjD,CAAC;IAED;;OAEG;IACH,YAAY;QACV,OAAO;YACL,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI;YAC3B,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,WAAW;YACzC,SAAS,EAAE,IAAI,CAAC,WAAW,CAAC,SAAS;YACrC,aAAa,EAAE,IAAI,CAAC,WAAW,CAAC,aAAa;YAC7C,cAAc,EAAE,IAAI,CAAC,WAAW,CAAC,cAAc;YAC/C,qBAAqB,EAAE,IAAI,CAAC,WAAW,CAAC,qBAAqB;SAC9D,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CAAC,IAAY;QAC5B,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YAClD,OAAO,MAAM,CAAC,WAAW,CAAC;QAC5B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,sDAAsD;YACtD,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACpC,CAAC;IACH,CAAC;CACF;AAED,MAAM,OAAO,gBAAgB;IACnB,IAAI,CAAM,CAAC,4BAA4B;IACvC,MAAM,CAAS;IAEvB,YAAY,IAAS,EAAE,MAAc;QACnC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CACf,OAAe,EACf,UAAuB,EAAE;QAQzB,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,gBAAgB,CAAC,KAAK,IAAI,EAAE;gBAC/C,OAAO,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YACxC,CAAC,CAAC,CAAC;YAEH,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;YACjC,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;YAE7B,2BAA2B;YAC3B,MAAM,aAAa,GAAG,QAAQ,CAAC,aAAa,EAAE,EAAE,IAAI,EAAE,CAAC;YAEvD,OAAO;gBACL,IAAI;gBACJ,aAAa,EAAE,aAAa,CAAC,GAAG,CAAC,CAAC,IAAS,EAAE,EAAE,CAAC,CAAC;oBAC/C,IAAI,EAAE,IAAI,CAAC,IAAI;oBACf,IAAI,EAAE,IAAI,CAAC,IAAI;iBAChB,CAAC,CAAC;aACJ,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBAC3B,MAAM,IAAI,QAAQ,CAAC,wBAAwB,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAC9D,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,oBAAoB,CACxB,YAAoB,EACpB,QAA6B;QAE7B,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,gBAAgB,CAAC,KAAK,IAAI,EAAE;gBAC/C,OAAO,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC;oBAC3B;wBACE,gBAAgB,EAAE;4BAChB,IAAI,EAAE,YAAY;4BAClB,QAAQ;yBACT;qBACF;iBACF,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,OAAO,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;QAChC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBAC3B,MAAM,IAAI,QAAQ,CAAC,6BAA6B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YACnE,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,UAAU;QACR,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,GAAG,CAAC,CAAC,GAAQ,EAAE,EAAE,CAAC,CAAC;YAC/C,IAAI,EAAE,GAAG,CAAC,IAAI;YACd,KAAK,EAAE,GAAG,CAAC,KAAK;SACjB,CAAC,CAAC,CAAC;IACN,CAAC;CACF"}