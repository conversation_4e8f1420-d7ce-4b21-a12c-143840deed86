/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import esbuild from 'esbuild';
import path from 'path';
import { fileURLToPath } from 'url';
import { createRequire } from 'module';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const require = createRequire(import.meta.url);
const pkg = require(path.resolve(__dirname, 'package.json'));

esbuild
  .build({
    entryPoints: ['packages/cli/index.ts'],
    bundle: true,
    outfile: 'bundle/arien.js',
    platform: 'node',
    format: 'esm',
    target: 'node18',
    loader: {
      '.js': 'jsx',
      '.ts': 'tsx',
    },
    define: {
      'process.env.CLI_VERSION': JSON.stringify(pkg.version),
    },
    banner: {
      js: `import { createRequire } from 'module'; const ___require = createRequire(import.meta.url); const ___url = ___require('url'); const ___path = ___require('path'); globalThis.__filename = ___url.fileURLToPath(import.meta.url); globalThis.__dirname = ___path.dirname(globalThis.__filename); const require = ___require;`,
    },
    external: [],
    packages: 'bundle',
  })
  .catch(() => process.exit(1));
