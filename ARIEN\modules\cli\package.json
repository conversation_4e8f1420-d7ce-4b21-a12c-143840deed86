{"name": "@arien/arien-ai-cli", "version": "1.0.0", "description": "ARIEN AI CLI - Interactive command line interface for AI-powered development", "repository": {"type": "git", "url": "https://github.com/arien-ai/arien-cli"}, "type": "module", "main": "dist/index.js", "types": "dist/index.d.ts", "bin": {"arien": "dist/index.js"}, "scripts": {"build": "node ../../scripts/build-module.js", "clean": "rm -rf dist", "start": "node dist/index.js", "debug": "node --inspect-brk dist/index.js", "lint": "eslint . --ext .ts,.tsx", "format": "prettier --write .", "test": "vitest run", "test:ci": "vitest run --coverage", "test:watch": "vitest", "typecheck": "tsc --noEmit", "prerelease:version": "node ../../scripts/bind-module-version.js", "prerelease:deps": "node ../../scripts/bind-module-dependencies.js", "prepack": "npm run build", "prepublishOnly": "node ../../scripts/prepublish.js"}, "files": ["dist", "README.md"], "keywords": ["ai", "cli", "interactive", "development", "assistant"], "config": {"sandboxImageUri": "arien-cli-sandbox"}, "dependencies": {"@arien/arien-ai-core": "*", "@types/update-notifier": "^6.0.8", "command-exists": "^1.2.9", "diff": "^7.0.0", "dotenv": "^16.6.1", "gaxios": "^6.1.1", "glob": "^10.4.1", "highlight.js": "^11.11.1", "ink": "^6.0.1", "ink-big-text": "^2.0.0", "ink-gradient": "^3.0.0", "ink-select-input": "^6.2.0", "ink-spinner": "^5.0.0", "ink-link": "^4.1.0", "ink-text-input": "^6.0.0", "lowlight": "^3.3.0", "mime-types": "^2.1.4", "open": "^10.1.2", "react": "^19.1.0", "read-package-up": "^11.0.0", "shell-quote": "^1.8.2", "string-width": "^7.1.0", "strip-ansi": "^7.1.0", "strip-json-comments": "^3.1.1", "update-notifier": "^7.3.1", "yargs": "^17.7.2"}, "devDependencies": {"@google/genai": "^1.4.0", "@testing-library/react": "^16.3.0", "@babel/runtime": "^7.27.6", "@types/command-exists": "^1.2.3", "@types/diff": "^7.0.2", "@types/dotenv": "^6.1.1", "@types/node": "^20.11.24", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/shell-quote": "^1.7.5", "@types/yargs": "^17.0.32", "ink-testing-library": "^4.0.0", "jsdom": "^26.1.0", "react-dom": "^19.1.0", "pretty-format": "^30.0.2", "typescript": "^5.3.3", "vitest": "^3.1.1"}, "engines": {"node": ">=18"}}