/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */

import esbuild from 'esbuild';
import path from 'path';
import { fileURLToPath } from 'url';
import { createRequire } from 'module';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const require = createRequire(import.meta.url);
const pkg = require(path.resolve(__dirname, 'package.json'));

esbuild
  .build({
    entryPoints: ['modules/cli/index.ts'],
    bundle: true,
    outfile: 'bundle/arien.js',
    platform: 'node',
    format: 'esm',
    target: 'node18',
    loader: {
      '.js': 'jsx',
      '.ts': 'tsx',
    },
    define: {
      'process.env.CLI_VERSION': JSON.stringify(pkg.version),
      'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'production'),
    },
    banner: {
      js: `#!/usr/bin/env node
import { createRequire } from 'module'; 
const ___require = createRequire(import.meta.url); 
const ___url = ___require('url'); 
const ___path = ___require('path'); 
globalThis.__filename = ___url.fileURLToPath(import.meta.url); 
globalThis.__dirname = ___path.dirname(globalThis.__filename); 
const require = ___require;`,
    },
    external: [],
    packages: 'bundle',
    minify: process.env.NODE_ENV === 'production',
    sourcemap: process.env.NODE_ENV !== 'production',
    metafile: true,
  })
  .then((result) => {
    console.log('✅ Bundle created successfully');
    if (result.metafile) {
      console.log('📊 Bundle analysis:');
      console.log(`   Output: ${result.metafile.outputs['bundle/arien.js']?.bytes || 0} bytes`);
    }
  })
  .catch((error) => {
    console.error('❌ Bundle failed:', error);
    process.exit(1);
  });
