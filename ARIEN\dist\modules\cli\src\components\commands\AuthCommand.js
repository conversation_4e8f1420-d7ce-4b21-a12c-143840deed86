import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */
import { useState, useEffect } from 'react';
import { Box, Text } from 'ink';
import { useTheme } from '../ui/ThemeProvider.js';
import { LoadingSpinner } from '../ui/LoadingSpinner.js';
import { ErrorDisplay } from '../ui/ErrorDisplay.js';
export const AuthCommand = ({ config, args, telemetry, logger, onExit, }) => {
    const theme = useTheme();
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState(null);
    const [result, setResult] = useState(null);
    useEffect(() => {
        const executeCommand = async () => {
            try {
                setIsLoading(true);
                telemetry.logEvent({
                    name: 'auth.command',
                    properties: {
                        subcommand: args.subcommand,
                        args: args.positional,
                    },
                });
                // TODO: Implement authentication commands
                setResult(`Authentication command: ${args.subcommand || 'status'}
(Authentication functionality would be implemented here)`);
            }
            catch (err) {
                const errorMessage = err instanceof Error ? err.message : String(err);
                logger.error('Auth command failed', { error: errorMessage }, 'AuthCommand');
                setError(errorMessage);
            }
            finally {
                setIsLoading(false);
            }
        };
        executeCommand();
    }, [args.subcommand, args.positional, logger, telemetry]);
    // Auto-exit after showing result
    useEffect(() => {
        if (result && !isLoading) {
            const timer = setTimeout(() => {
                onExit();
            }, 3000);
            return () => clearTimeout(timer);
        }
        return () => { }; // Return empty cleanup function for other cases
    }, [result, isLoading, onExit]);
    if (isLoading) {
        return (_jsx(Box, { padding: 1, children: _jsx(LoadingSpinner, { text: "Processing authentication..." }) }));
    }
    if (error) {
        return (_jsx(Box, { padding: 1, children: _jsx(ErrorDisplay, { error: error, onRetry: () => setError(null) }) }));
    }
    if (result) {
        return (_jsxs(Box, { flexDirection: "column", padding: 1, children: [_jsxs(Text, { color: theme.colors.warning, children: [theme.symbols.info, " Authentication"] }), _jsx(Box, { marginTop: 1, children: _jsx(Text, { children: result }) })] }));
    }
    return (_jsx(Box, { padding: 1, children: _jsx(Text, { color: theme.colors.textSecondary, children: "No result" }) }));
};
