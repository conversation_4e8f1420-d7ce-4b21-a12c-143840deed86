{"version": 3, "file": "MetricExporter.js", "sourceRoot": "", "sources": ["../../../src/export/MetricExporter.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { AggregationTemporality } from './AggregationTemporality';\nimport { ResourceMetrics } from './MetricData';\nimport { ExportResult } from '@opentelemetry/core';\nimport { InstrumentType } from '../InstrumentDescriptor';\nimport { Aggregation } from '../view/Aggregation';\n\n/**\n * An interface that allows different metric services to export recorded data\n * in their own format.\n *\n * To export data this MUST be registered to the Metrics SDK with a MetricReader.\n */\nexport interface PushMetricExporter {\n  /**\n   * Called to export sampled {@link ResourceMetrics}.\n   * @param metrics the metric data to be exported.\n   * @param resultCallback callback for when the export has completed\n   */\n  export(\n    metrics: ResourceMetrics,\n    resultCallback: (result: ExportResult) => void\n  ): void;\n\n  /**\n   * Ensure that the export of any metrics the exporter has received is\n   * completed before the returned promise is settled.\n   */\n  forceFlush(): Promise<void>;\n\n  /**\n   * Select the {@link AggregationTemporality} for the given\n   * {@link InstrumentType} for this exporter.\n   */\n  selectAggregationTemporality?(\n    instrumentType: InstrumentType\n  ): AggregationTemporality;\n\n  /**\n   * Select the {@link Aggregation} for the given\n   * {@link InstrumentType} for this exporter.\n   */\n  selectAggregation?(instrumentType: InstrumentType): Aggregation;\n\n  /**\n   * Returns a promise which resolves when the last exportation is completed.\n   * Further calls to {@link PushMetricExporter.export} may not export the\n   * data.\n   */\n  shutdown(): Promise<void>;\n}\n"]}