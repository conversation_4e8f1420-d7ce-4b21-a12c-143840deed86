/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import React from 'react';
import { Box, Text } from 'ink';
import { Colors } from '../colors.js';
import { shortenPath, tildeifyPath, tokenLimit } from '@arien/arien-cli-core';
import { ConsoleSummaryDisplay } from './ConsoleSummaryDisplay.js';
import process from 'node:process';
import { MemoryUsageDisplay } from './MemoryUsageDisplay.js';

interface FooterProps {
  model: string;
  targetDir: string;
  branchName?: string;
  debugMode: boolean;
  debugMessage: string;
  corgiMode: boolean;
  errorCount: number;
  showErrorDetails: boolean;
  showMemoryUsage?: boolean;
  promptTokenCount: number;
  candidatesTokenCount: number;
  totalTokenCount: number;
}

export const Footer: React.FC<FooterProps> = ({
  model,
  targetDir,
  branchName,
  debugMode,
  debugMessage,
  corgiMode,
  errorCount,
  showErrorDetails,
  showMemoryUsage,
  totalTokenCount,
}) => {
  const limit = tokenLimit(model);
  const percentage = totalTokenCount / limit;
  const contextLeft = ((1 - percentage) * 100).toFixed(0);
  
  // Enhanced visual indicator for context usage
  const getContextColor = () => {
    if (percentage > 0.9) return Colors.AccentRed;
    if (percentage > 0.7) return Colors.AccentYellow;
    return Colors.AccentGreen;
  };

  const getContextIndicator = () => {
    if (percentage > 0.9) return ' ⚠';
    if (percentage > 0.7) return ' ⚡';
    return '';
  };

  return (
    <Box marginTop={1} justifyContent="space-between" width="100%">
      <Box flexDirection="column">
        <Box>
          <Text color={Colors.LightBlue}>
            {shortenPath(tildeifyPath(targetDir), 70)}
            {branchName && <Text color={Colors.Gray}> ({branchName}*)</Text>}
          </Text>
        </Box>
        {debugMode && (
          <Box>
            <Text color={Colors.AccentRed}>
              {debugMessage || '--debug mode active'}
            </Text>
          </Box>
        )}
      </Box>

      {/* Middle Section: Centered Sandbox Info */}
      <Box
        flexGrow={1}
        alignItems="center"
        justifyContent="center"
        display="flex"
      >
        {process.env.SANDBOX && process.env.SANDBOX !== 'sandbox-exec' ? (
          <Text color="green">
            {process.env.SANDBOX.replace(/^arien-(?:cli-)?/, '')}
          </Text>
        ) : process.env.SANDBOX === 'sandbox-exec' ? (
          <Text color={Colors.AccentYellow}>
            MacOS Seatbelt{' '}
            <Text color={Colors.Gray}>({process.env.SEATBELT_PROFILE})</Text>
          </Text>
        ) : (
          <Text color={Colors.AccentRed}>
            no sandbox <Text color={Colors.Gray}>(see /docs)</Text>
          </Text>
        )}
      </Box>

              {/* Right Section: Enhanced Model and Context Info */}
      <Box alignItems="center" flexDirection="column">
        <Box alignItems="center">
          <Text color={Colors.AccentBlue}>
            {model}
          </Text>
          <Text color={getContextColor()}>
            {' '}({contextLeft}% context left{getContextIndicator()})
          </Text>
        </Box>
        <Box alignItems="center">
          {corgiMode && (
            <Text>
              <Text color={Colors.Gray}>| </Text>
              <Text color={Colors.AccentRed}>[CORGI MODE]</Text>
            </Text>
          )}
          {!showErrorDetails && errorCount > 0 && (
            <Box>
              <Text color={Colors.Gray}>| </Text>
              <ConsoleSummaryDisplay errorCount={errorCount} />
            </Box>
          )}
          {showMemoryUsage && <MemoryUsageDisplay />}
        </Box>
      </Box>
    </Box>
  );
};
