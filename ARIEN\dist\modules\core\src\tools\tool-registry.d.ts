/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */
import { ToolDefinition, ToolContext, ToolResult, ToolExecutionStats, ToolApprovalRequest, ToolApprovalResponse, ToolExecutionMode } from './tools.js';
export declare class ToolRegistry {
    private tools;
    private stats;
    private approvalHandler?;
    private executionMode;
    /**
     * Register a tool
     */
    register(tool: ToolDefinition): void;
    /**
     * Unregister a tool
     */
    unregister(toolName: string): boolean;
    /**
     * Get a tool by name
     */
    getTool(name: string): ToolDefinition | undefined;
    /**
     * Get all registered tools
     */
    getAllTools(): ToolDefinition[];
    /**
     * Get tools by category
     */
    getToolsByCategory(category: string): ToolDefinition[];
    /**
     * Get tool definitions for AI model registration
     */
    getToolDefinitions(): Array<{
        name: string;
        description: string;
        parameters: {
            type: 'object';
            properties: Record<string, any>;
            required?: string[];
        };
    }>;
    /**
     * Set the execution mode
     */
    setExecutionMode(mode: ToolExecutionMode): void;
    /**
     * Set the approval handler
     */
    setApprovalHandler(handler: (request: ToolApprovalRequest) => Promise<ToolApprovalResponse>): void;
    /**
     * Execute a tool
     */
    executeTool(toolName: string, parameters: any, context: ToolContext): Promise<ToolResult>;
    /**
     * Execute multiple tools in sequence
     */
    executeTools(toolCalls: Array<{
        name: string;
        parameters: any;
    }>, context: ToolContext): Promise<ToolResult[]>;
    /**
     * Get execution statistics for a tool
     */
    getToolStats(toolName: string): ToolExecutionStats | undefined;
    /**
     * Get execution statistics for all tools
     */
    getAllStats(): ToolExecutionStats[];
    /**
     * Reset statistics for a tool
     */
    resetStats(toolName?: string): void;
    /**
     * Update execution statistics
     */
    private updateStats;
    /**
     * Check if a tool exists
     */
    hasTool(toolName: string): boolean;
    /**
     * Get tool count
     */
    getToolCount(): number;
    /**
     * Clear all tools
     */
    clear(): void;
}
/**
 * Get the global tool registry
 */
export declare function getToolRegistry(): ToolRegistry;
/**
 * Set the global tool registry
 */
export declare function setToolRegistry(registry: ToolRegistry): void;
