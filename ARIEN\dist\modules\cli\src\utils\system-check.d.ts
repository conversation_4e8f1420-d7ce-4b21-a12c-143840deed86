/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */
export interface SystemInfo {
    platform: string;
    arch: string;
    nodeVersion: string;
    npmVersion?: string;
    gitVersion?: string;
    hasInternet: boolean;
    terminalSupport: {
        colors: boolean;
        unicode: boolean;
        interactive: boolean;
    };
}
/**
 * Check system requirements and compatibility
 */
export declare function checkSystemRequirements(): Promise<SystemInfo>;
/**
 * Check if required directories exist and are writable
 */
export declare function checkDirectoryPermissions(): void;
/**
 * Get system information for debugging
 */
export declare function getSystemInfo(): Record<string, any>;
/**
 * Check if running in a supported environment
 */
export declare function checkEnvironmentSupport(): void;
