/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */
export declare class ArienError extends Error {
    readonly code: string;
    readonly details?: Record<string, unknown>;
    constructor(message: string, code: string, details?: Record<string, unknown>);
}
export declare class ConfigurationError extends ArienError {
    constructor(message: string, details?: Record<string, unknown>);
}
export declare class AuthenticationError extends ArienError {
    constructor(message: string, details?: Record<string, unknown>);
}
export declare class ApiError extends ArienError {
    readonly statusCode?: number;
    constructor(message: string, statusCode?: number, details?: Record<string, unknown>);
}
export declare class ToolExecutionError extends ArienError {
    readonly toolName: string;
    constructor(message: string, toolName: string, details?: Record<string, unknown>);
}
export declare class ValidationError extends ArienError {
    readonly field?: string;
    constructor(message: string, field?: string, details?: Record<string, unknown>);
}
export declare class FileSystemError extends ArienError {
    readonly path?: string;
    constructor(message: string, path?: string, details?: Record<string, unknown>);
}
export declare class NetworkError extends ArienError {
    readonly url?: string;
    constructor(message: string, url?: string, details?: Record<string, unknown>);
}
export declare class SandboxError extends ArienError {
    constructor(message: string, details?: Record<string, unknown>);
}
export declare class TimeoutError extends ArienError {
    readonly timeoutMs: number;
    constructor(message: string, timeoutMs: number);
}
/**
 * Utility function to check if an error is of a specific type
 */
export declare function isArienError(error: unknown): error is ArienError;
/**
 * Utility function to extract error message from any error type
 */
export declare function getErrorMessage(error: unknown): string;
/**
 * Utility function to extract error details for logging
 */
export declare function getErrorDetails(error: unknown): Record<string, unknown>;
/**
 * Utility function to create a standardized error response
 */
export declare function createErrorResponse(error: unknown): {
    success: false;
    error: {
        message: string;
        code?: string;
        details?: Record<string, unknown>;
    };
};
