{"version": 3, "file": "client.d.ts", "sourceRoot": "", "sources": ["../../src/core/client.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAGH,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAK7C,MAAM,WAAW,WAAW;IAC1B,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC;IACvB,KAAK,EAAE,KAAK,CAAC;QACX,IAAI,CAAC,EAAE,MAAM,CAAC;QACd,UAAU,CAAC,EAAE;YACX,QAAQ,EAAE,MAAM,CAAC;YACjB,IAAI,EAAE,MAAM,CAAC;SACd,CAAC;QACF,YAAY,CAAC,EAAE;YACb,IAAI,EAAE,MAAM,CAAC;YACb,IAAI,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;SAC3B,CAAC;QACF,gBAAgB,CAAC,EAAE;YACjB,IAAI,EAAE,MAAM,CAAC;YACb,QAAQ,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;SAC/B,CAAC;KACH,CAAC,CAAC;CACJ;AAED,MAAM,WAAW,WAAW;IAC1B,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,eAAe,CAAC,EAAE,MAAM,CAAC;IACzB,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,aAAa,CAAC,EAAE,MAAM,EAAE,CAAC;CAC1B;AAED,MAAM,WAAW,cAAc;IAC7B,IAAI,EAAE,MAAM,CAAC;IACb,WAAW,EAAE,MAAM,CAAC;IACpB,UAAU,EAAE;QACV,IAAI,EAAE,QAAQ,CAAC;QACf,UAAU,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;QAChC,QAAQ,CAAC,EAAE,MAAM,EAAE,CAAC;KACrB,CAAC;CACH;AAED,qBAAa,WAAW;IACtB,OAAO,CAAC,KAAK,CAAqB;IAClC,OAAO,CAAC,KAAK,CAAkB;IAC/B,OAAO,CAAC,MAAM,CAAS;IACvB,OAAO,CAAC,WAAW,CAAoC;gBAE3C,MAAM,EAAE,MAAM;IAkB1B,OAAO,CAAC,SAAS;IAiBjB,OAAO,CAAC,mBAAmB;IAS3B;;OAEG;IACG,eAAe,CACnB,MAAM,EAAE,MAAM,EACd,OAAO,GAAE,WAAgB,GACxB,OAAO,CAAC,MAAM,CAAC;IA8BlB;;OAEG;IACG,SAAS,CACb,OAAO,GAAE,WAAW,EAAO,EAC3B,KAAK,GAAE,cAAc,EAAO,GAC3B,OAAO,CAAC,gBAAgB,CAAC;IAmB5B;;OAEG;IACH,YAAY;;;;;;;;IAWZ;;OAEG;IACG,WAAW,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;CASjD;AAED,qBAAa,gBAAgB;IAC3B,OAAO,CAAC,IAAI,CAAM;IAClB,OAAO,CAAC,MAAM,CAAS;gBAEX,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM;IAKrC;;OAEG;IACG,WAAW,CACf,OAAO,EAAE,MAAM,EACf,OAAO,GAAE,WAAgB,GACxB,OAAO,CAAC;QACT,IAAI,EAAE,MAAM,CAAC;QACb,aAAa,CAAC,EAAE,KAAK,CAAC;YACpB,IAAI,EAAE,MAAM,CAAC;YACb,IAAI,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;SAC3B,CAAC,CAAC;KACJ,CAAC;IA2BF;;OAEG;IACG,oBAAoB,CACxB,YAAY,EAAE,MAAM,EACpB,QAAQ,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GAC5B,OAAO,CAAC,MAAM,CAAC;IAsBlB;;OAEG;IACH,UAAU,IAAI,WAAW,EAAE;CAM5B"}