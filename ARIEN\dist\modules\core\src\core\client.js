/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */
import { GoogleGenerativeAI } from '@google/generative-ai';
import { getModelConfig } from '../config/models.js';
import { AuthenticationError, ApiError } from '../utils/errors.js';
import { retryWithBackoff } from '../utils/retry.js';
export class ArienClient {
    genAI;
    model;
    config;
    modelConfig;
    constructor(config) {
        this.config = config;
        const apiKey = this.getApiKey();
        if (!apiKey) {
            throw new AuthenticationError('No API key provided. Set ARIEN_API_KEY environment variable or configure authentication.');
        }
        this.genAI = new GoogleGenerativeAI(apiKey);
        this.modelConfig = getModelConfig(config.getModel());
        // Initialize the model with default configuration
        this.model = this.genAI.getGenerativeModel({
            model: this.modelConfig.name,
            generationConfig: this.getGenerationConfig(),
        });
    }
    getApiKey() {
        const authType = this.config.getAuthType();
        switch (authType) {
            case 'api-key':
                return this.config.getApiKey() || '';
            case 'service-account':
                // TODO: Implement service account authentication
                throw new AuthenticationError('Service account authentication not yet implemented');
            case 'oauth':
                // TODO: Implement OAuth authentication
                throw new AuthenticationError('OAuth authentication not yet implemented');
            default:
                throw new AuthenticationError(`Unknown authentication type: ${authType}`);
        }
    }
    getGenerationConfig() {
        return {
            temperature: this.config.getTemperature(),
            maxOutputTokens: this.config.getMaxTokens(),
            topP: 0.95,
            topK: 40,
        };
    }
    convertToolsToFunctionDeclarations(tools) {
        return tools.map(tool => ({
            name: tool.name,
            description: tool.description,
            parameters: {
                type: 'object',
                properties: tool.parameters._def?.shape || {},
                required: Object.keys(tool.parameters._def?.shape || {})
            }
        }));
    }
    /**
     * Generate content using the AI model
     */
    async generateContent(prompt, options = {}) {
        try {
            const generationConfig = {
                ...this.getGenerationConfig(),
                ...options,
            };
            const result = await retryWithBackoff(async () => {
                return this.model.generateContent({
                    contents: [{ role: 'user', parts: [{ text: prompt }] }],
                    generationConfig,
                });
            });
            const response = result.response;
            const text = response.text();
            if (!text) {
                throw new ApiError('Empty response from AI model');
            }
            return text;
        }
        catch (error) {
            if (error instanceof Error) {
                throw new ApiError(`AI generation failed: ${error.message}`);
            }
            throw error;
        }
    }
    /**
     * Start a chat session
     */
    async startChat(history = [], tools = []) {
        const modelWithTools = tools.length > 0
            ? this.genAI.getGenerativeModel({
                model: this.modelConfig.name,
                generationConfig: this.getGenerationConfig(),
                tools: [{ functionDeclarations: this.convertToolsToFunctionDeclarations(tools) }],
            })
            : this.model;
        const chat = modelWithTools.startChat({
            history: history.map(msg => ({
                role: msg.role,
                parts: msg.parts,
            })),
        });
        return new ArienChatSession(chat, this.config);
    }
    /**
     * Get model information
     */
    getModelInfo() {
        return {
            name: this.modelConfig.name,
            displayName: this.modelConfig.displayName,
            maxTokens: this.modelConfig.maxTokens,
            supportsTools: this.modelConfig.supportsTools,
            supportsVision: this.modelConfig.supportsVision,
            supportsCodeExecution: this.modelConfig.supportsCodeExecution,
        };
    }
    /**
     * Estimate token count for text
     */
    async countTokens(text) {
        try {
            const result = await this.model.countTokens(text);
            return result.totalTokens;
        }
        catch (error) {
            // Fallback estimation: roughly 4 characters per token
            return Math.ceil(text.length / 4);
        }
    }
}
export class ArienChatSession {
    chat; // GoogleAI ChatSession type
    config;
    constructor(chat, config) {
        this.chat = chat;
        this.config = config;
    }
    /**
     * Send a message in the chat session
     */
    async sendMessage(message, _options = {}) {
        try {
            const result = await retryWithBackoff(async () => {
                return this.chat.sendMessage(message);
            });
            const response = result.response;
            const text = response.text();
            // Check for function calls
            const functionCalls = response.functionCalls?.() || [];
            return {
                text,
                functionCalls: functionCalls.map((call) => ({
                    name: call.name,
                    args: call.args,
                })),
            };
        }
        catch (error) {
            if (error instanceof Error) {
                throw new ApiError(`Chat message failed: ${error.message}`);
            }
            throw error;
        }
    }
    /**
     * Send a function response
     */
    async sendFunctionResponse(functionName, response) {
        try {
            const result = await retryWithBackoff(async () => {
                return this.chat.sendMessage([
                    {
                        functionResponse: {
                            name: functionName,
                            response,
                        },
                    },
                ]);
            });
            return result.response.text();
        }
        catch (error) {
            if (error instanceof Error) {
                throw new ApiError(`Function response failed: ${error.message}`);
            }
            throw error;
        }
    }
    /**
     * Get chat history
     */
    getHistory() {
        return this.chat.getHistory().map((msg) => ({
            role: msg.role,
            parts: msg.parts,
        }));
    }
}
