/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import React from 'react';
import { Text } from 'ink';
import { Colors } from '../colors.js';
import { type MCPServerConfig } from '@arien/arien-cli-core';

interface ContextSummaryDisplayProps {
  arienMdFileCount: number;
  contextFileNames: string[];
  mcpServers?: Record<string, MCPServerConfig>;
  showToolDescriptions?: boolean;
}

export const ContextSummaryDisplay: React.FC<ContextSummaryDisplayProps> = ({
  arienMdFileCount,
  contextFileNames,
  mcpServers,
  showToolDescriptions,
}) => {
  const mcpServerCount = Object.keys(mcpServers || {}).length;

  if (arienMdFileCount === 0 && mcpServerCount === 0) {
    return <Text color={Colors.Gray}>No context loaded</Text>;
  }

  const contextElements: React.ReactNode[] = [];

  // Context files
  if (arienMdFileCount > 0) {
    const allNamesTheSame = new Set(contextFileNames).size < 2;
    const name = allNamesTheSame ? contextFileNames[0] : 'context';
    contextElements.push(
      <Text key="context" color={Colors.AccentCyan}>
        {arienMdFileCount} {name} file{arienMdFileCount > 1 ? 's' : ''}
      </Text>
    );
  }

  // MCP servers
  if (mcpServerCount > 0) {
    const mcpElement = (
      <Text key="mcp" color={Colors.AccentPurple}>
        {mcpServerCount} MCP server{mcpServerCount > 1 ? 's' : ''}
        {mcpServers && Object.keys(mcpServers).length > 0 && (
          <Text color={Colors.Gray} dimColor>
            {showToolDescriptions ? ' (Ctrl+T to hide)' : ' (Ctrl+T to view)'}
          </Text>
        )}
      </Text>
    );
    contextElements.push(mcpElement);
  }

  return (
    <Text>
      <Text color={Colors.Gray}>Context: </Text>
      {contextElements.map((element, index) => (
        <React.Fragment key={index}>
          {index > 0 && <Text color={Colors.Gray}> • </Text>}
          {element}
        </React.Fragment>
      ))}
    </Text>
  );
};
