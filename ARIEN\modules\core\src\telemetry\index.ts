/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */

export * from './types.js';
export * from './metrics.js';
export * from './loggers.js';

import { Config } from '../config/config.js';
import { TelemetryLogger } from './loggers.js';
import { MetricsCollector } from './metrics.js';
import { TelemetryEvent, TelemetryMetric } from './types.js';

export class TelemetryManager {
  private logger: TelemetryLogger;
  private metrics: MetricsCollector;
  private enabled: boolean;

  constructor(config: Config) {
    this.enabled = config.isTelemetryEnabled();
    this.logger = new TelemetryLogger(this.enabled);
    this.metrics = new MetricsCollector(this.enabled);
  }

  /**
   * Log a telemetry event
   */
  logEvent(event: TelemetryEvent): void {
    if (!this.enabled) return;
    this.logger.logEvent(event);
  }

  /**
   * Record a metric
   */
  recordMetric(metric: TelemetryMetric): void {
    if (!this.enabled) return;
    this.metrics.recordMetric(metric);
  }

  /**
   * Start timing an operation
   */
  startTimer(name: string): () => void {
    if (!this.enabled) return () => {};
    return this.metrics.startTimer(name);
  }

  /**
   * Record a counter increment
   */
  incrementCounter(name: string, value = 1, tags?: Record<string, string>): void {
    if (!this.enabled) return;
    this.metrics.incrementCounter(name, value, tags);
  }

  /**
   * Record a gauge value
   */
  recordGauge(name: string, value: number, tags?: Record<string, string>): void {
    if (!this.enabled) return;
    this.metrics.recordGauge(name, value, tags);
  }

  /**
   * Get collected metrics
   */
  getMetrics(): TelemetryMetric[] {
    return this.metrics.getMetrics();
  }

  /**
   * Get logged events
   */
  getEvents(): TelemetryEvent[] {
    return this.logger.getEvents();
  }

  /**
   * Clear all telemetry data
   */
  clear(): void {
    this.logger.clear();
    this.metrics.clear();
  }

  /**
   * Enable or disable telemetry
   */
  setEnabled(enabled: boolean): void {
    this.enabled = enabled;
    this.logger.setEnabled(enabled);
    this.metrics.setEnabled(enabled);
  }

  /**
   * Check if telemetry is enabled
   */
  isEnabled(): boolean {
    return this.enabled;
  }

  /**
   * Export telemetry data
   */
  export(): {
    events: TelemetryEvent[];
    metrics: TelemetryMetric[];
    timestamp: Date;
  } {
    return {
      events: this.getEvents(),
      metrics: this.getMetrics(),
      timestamp: new Date(),
    };
  }
}

// Global telemetry manager
let globalTelemetry: TelemetryManager | null = null;

/**
 * Get the global telemetry manager
 */
export function getTelemetry(): TelemetryManager | null {
  return globalTelemetry;
}

/**
 * Initialize telemetry with configuration
 */
export function initTelemetry(config: Config): TelemetryManager {
  globalTelemetry = new TelemetryManager(config);
  return globalTelemetry;
}

/**
 * Convenience functions for common telemetry operations
 */
export function logEvent(event: TelemetryEvent): void {
  globalTelemetry?.logEvent(event);
}

export function recordMetric(metric: TelemetryMetric): void {
  globalTelemetry?.recordMetric(metric);
}

export function startTimer(name: string): () => void {
  return globalTelemetry?.startTimer(name) || (() => {});
}

export function incrementCounter(name: string, value = 1, tags?: Record<string, string>): void {
  globalTelemetry?.incrementCounter(name, value, tags);
}

export function recordGauge(name: string, value: number, tags?: Record<string, string>): void {
  globalTelemetry?.recordGauge(name, value, tags);
}
