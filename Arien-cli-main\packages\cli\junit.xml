<?xml version="1.0" encoding="UTF-8" ?>
<testsuites name="vitest tests" tests="16" failures="0" errors="0" time="0.0800114">
    <testsuite name="src/ui/components/messages/ToolMessage.test.tsx" timestamp="2025-07-01T16:36:22.519Z" hostname="Ajayk" tests="16" failures="0" errors="0" skipped="0" time="0.0800114">
        <testcase classname="src/ui/components/messages/ToolMessage.test.tsx" name="&lt;ToolMessage /&gt; &gt; renders basic tool information" time="0.0285155">
        </testcase>
        <testcase classname="src/ui/components/messages/ToolMessage.test.tsx" name="&lt;ToolMessage /&gt; &gt; ToolStatusIndicator rendering &gt; shows ✓ for Success status" time="0.0040419">
        </testcase>
        <testcase classname="src/ui/components/messages/ToolMessage.test.tsx" name="&lt;ToolMessage /&gt; &gt; ToolStatusIndicator rendering &gt; shows ◦ for Pending status" time="0.0022923">
        </testcase>
        <testcase classname="src/ui/components/messages/ToolMessage.test.tsx" name="&lt;ToolMessage /&gt; &gt; ToolStatusIndicator rendering &gt; shows ⚡ for Confirming status" time="0.0028506">
        </testcase>
        <testcase classname="src/ui/components/messages/ToolMessage.test.tsx" name="&lt;ToolMessage /&gt; &gt; ToolStatusIndicator rendering &gt; shows ⊘ for Canceled status" time="0.0029472">
        </testcase>
        <testcase classname="src/ui/components/messages/ToolMessage.test.tsx" name="&lt;ToolMessage /&gt; &gt; ToolStatusIndicator rendering &gt; shows ✗ for Error status" time="0.00261">
        </testcase>
        <testcase classname="src/ui/components/messages/ToolMessage.test.tsx" name="&lt;ToolMessage /&gt; &gt; ToolStatusIndicator rendering &gt; shows executing symbol for Executing status when streamingState is Idle" time="0.0035338">
        </testcase>
        <testcase classname="src/ui/components/messages/ToolMessage.test.tsx" name="&lt;ToolMessage /&gt; &gt; ToolStatusIndicator rendering &gt; shows executing symbol for Executing status when streamingState is WaitingForConfirmation" time="0.0027422">
        </testcase>
        <testcase classname="src/ui/components/messages/ToolMessage.test.tsx" name="&lt;ToolMessage /&gt; &gt; ToolStatusIndicator rendering &gt; shows MockRespondingSpinner for Executing status when streamingState is Responding" time="0.0036269">
        </testcase>
        <testcase classname="src/ui/components/messages/ToolMessage.test.tsx" name="&lt;ToolMessage /&gt; &gt; renders DiffRenderer for diff results" time="0.0034402">
        </testcase>
        <testcase classname="src/ui/components/messages/ToolMessage.test.tsx" name="&lt;ToolMessage /&gt; &gt; renders emphasis correctly" time="0.0059454">
        </testcase>
        <testcase classname="src/ui/components/messages/ToolMessage.test.tsx" name="&lt;ToolMessage /&gt; &gt; handles long result display by truncating" time="0.0029478">
        </testcase>
        <testcase classname="src/ui/components/messages/ToolMessage.test.tsx" name="&lt;ToolMessage /&gt; &gt; renders without result display" time="0.0015293">
        </testcase>
        <testcase classname="src/ui/components/messages/ToolMessage.test.tsx" name="&lt;ToolMessage /&gt; &gt; respects availableTerminalHeight constraint" time="0.0055508">
        </testcase>
        <testcase classname="src/ui/components/messages/ToolMessage.test.tsx" name="&lt;ToolMessage /&gt; &gt; handles empty result display" time="0.0016697">
        </testcase>
        <testcase classname="src/ui/components/messages/ToolMessage.test.tsx" name="&lt;ToolMessage /&gt; &gt; handles different terminal widths" time="0.0038719">
        </testcase>
    </testsuite>
</testsuites>
