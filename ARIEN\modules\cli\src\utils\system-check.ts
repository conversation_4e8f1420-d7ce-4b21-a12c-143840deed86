/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */

import { execSync } from 'child_process';
import { existsSync } from 'fs';
import { platform, arch, version as nodeVersion } from 'os';
import { getLogger } from '@arien/arien-ai-core';

export interface SystemInfo {
  platform: string;
  arch: string;
  nodeVersion: string;
  npmVersion?: string;
  gitVersion?: string;
  hasInternet: boolean;
  terminalSupport: {
    colors: boolean;
    unicode: boolean;
    interactive: boolean;
  };
}

/**
 * Check system requirements and compatibility
 */
export async function checkSystemRequirements(): Promise<SystemInfo> {
  const logger = getLogger();
  
  const systemInfo: SystemInfo = {
    platform: platform(),
    arch: arch(),
    nodeVersion: process.version,
    hasInternet: false,
    terminalSupport: {
      colors: false,
      unicode: false,
      interactive: false,
    },
  };
  
  try {
    // Check Node.js version
    const nodeVersionMatch = process.version.match(/^v(\d+)\.(\d+)\.(\d+)/);
    if (nodeVersionMatch) {
      const [, major, minor] = nodeVersionMatch;
      const majorVersion = parseInt(major, 10);
      const minorVersion = parseInt(minor, 10);
      
      if (majorVersion < 18) {
        throw new Error(`Node.js version ${process.version} is not supported. Please upgrade to Node.js 18 or later.`);
      }
      
      if (majorVersion === 18 && minorVersion < 0) {
        console.warn(`Warning: Node.js ${process.version} may have compatibility issues. Consider upgrading to a newer version.`);
      }
    }
    
    // Check npm version
    try {
      const npmVersionOutput = execSync('npm --version', { encoding: 'utf8', timeout: 5000 });
      systemInfo.npmVersion = npmVersionOutput.trim();
    } catch (error) {
      logger.warn('npm not found or not accessible');
    }
    
    // Check git version
    try {
      const gitVersionOutput = execSync('git --version', { encoding: 'utf8', timeout: 5000 });
      const gitVersionMatch = gitVersionOutput.match(/git version (\d+\.\d+\.\d+)/);
      if (gitVersionMatch) {
        systemInfo.gitVersion = gitVersionMatch[1];
      }
    } catch (error) {
      logger.warn('git not found or not accessible');
    }
    
    // Check internet connectivity
    systemInfo.hasInternet = await checkInternetConnectivity();
    
    // Check terminal support
    systemInfo.terminalSupport = checkTerminalSupport();
    
    // Log system information
    logger.info('System check completed', systemInfo);
    
    // Warn about potential issues
    if (!systemInfo.hasInternet) {
      console.warn('Warning: No internet connection detected. Some features may not work properly.');
    }
    
    if (!systemInfo.terminalSupport.colors) {
      console.warn('Warning: Terminal does not support colors. Output may appear plain.');
    }
    
    if (!systemInfo.terminalSupport.interactive) {
      console.warn('Warning: Terminal is not interactive. Some features may be limited.');
    }
    
    return systemInfo;
    
  } catch (error) {
    logger.error('System check failed', { error });
    throw error;
  }
}

/**
 * Check internet connectivity
 */
async function checkInternetConnectivity(): Promise<boolean> {
  try {
    // Try to resolve a reliable DNS name
    const { lookup } = await import('dns');
    const { promisify } = await import('util');
    const lookupAsync = promisify(lookup);
    
    await lookupAsync('google.com');
    return true;
  } catch (error) {
    return false;
  }
}

/**
 * Check terminal capabilities
 */
function checkTerminalSupport(): SystemInfo['terminalSupport'] {
  const support = {
    colors: false,
    unicode: false,
    interactive: false,
  };
  
  // Check color support
  const { env } = process;
  support.colors = !!(
    env.FORCE_COLOR ||
    env.COLORTERM ||
    env.TERM === 'xterm-256color' ||
    env.TERM === 'screen-256color' ||
    (env.TERM && env.TERM.includes('color')) ||
    process.stdout.isTTY
  );
  
  // Check Unicode support
  support.unicode = !!(
    env.LANG?.includes('UTF-8') ||
    env.LC_ALL?.includes('UTF-8') ||
    env.LC_CTYPE?.includes('UTF-8') ||
    process.platform === 'win32' // Windows generally supports Unicode
  );
  
  // Check if terminal is interactive
  support.interactive = !!(
    process.stdin.isTTY &&
    process.stdout.isTTY &&
    !env.CI && // Not in CI environment
    !env.NON_INTERACTIVE
  );
  
  return support;
}

/**
 * Check if required directories exist and are writable
 */
export function checkDirectoryPermissions(): void {
  const logger = getLogger();
  const { ensureArienDirs, getArienConfigDir } = require('@arien/arien-ai-core');
  
  try {
    // Ensure ARIEN directories exist
    ensureArienDirs();
    
    // Test write permissions
    const configDir = getArienConfigDir();
    const testFile = `${configDir}/.write-test-${Date.now()}`;
    
    try {
      const { writeFileSync, unlinkSync } = require('fs');
      writeFileSync(testFile, 'test');
      unlinkSync(testFile);
    } catch (error) {
      throw new Error(`Cannot write to configuration directory: ${configDir}`);
    }
    
    logger.debug('Directory permissions check passed');
    
  } catch (error) {
    logger.error('Directory permissions check failed', { error });
    throw error;
  }
}

/**
 * Get system information for debugging
 */
export function getSystemInfo(): Record<string, any> {
  const { env } = process;
  
  return {
    // Node.js info
    nodeVersion: process.version,
    platform: process.platform,
    arch: process.arch,
    
    // Environment info
    shell: env.SHELL || env.COMSPEC,
    term: env.TERM,
    colorterm: env.COLORTERM,
    lang: env.LANG,
    
    // Terminal info
    isTTY: {
      stdin: process.stdin.isTTY,
      stdout: process.stdout.isTTY,
      stderr: process.stderr.isTTY,
    },
    
    // CI detection
    ci: !!(
      env.CI ||
      env.CONTINUOUS_INTEGRATION ||
      env.BUILD_NUMBER ||
      env.GITHUB_ACTIONS ||
      env.GITLAB_CI ||
      env.CIRCLECI ||
      env.TRAVIS
    ),
    
    // Memory info
    memory: process.memoryUsage(),
    
    // Process info
    pid: process.pid,
    ppid: process.ppid,
    
    // Timing info
    uptime: process.uptime(),
  };
}

/**
 * Check if running in a supported environment
 */
export function checkEnvironmentSupport(): void {
  const logger = getLogger();
  
  // Check if running in a supported Node.js environment
  if (typeof process === 'undefined' || typeof require === 'undefined') {
    throw new Error('ARIEN CLI requires a Node.js environment');
  }
  
  // Check if running in browser (should not happen, but just in case)
  if (typeof window !== 'undefined') {
    throw new Error('ARIEN CLI cannot run in a browser environment');
  }
  
  // Check if all required Node.js APIs are available
  const requiredAPIs = ['fs', 'path', 'os', 'child_process', 'crypto'];
  for (const api of requiredAPIs) {
    try {
      require(api);
    } catch (error) {
      throw new Error(`Required Node.js API '${api}' is not available`);
    }
  }
  
  logger.debug('Environment support check passed');
}
