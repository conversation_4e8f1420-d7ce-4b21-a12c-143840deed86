/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */
import { writeFileSync, existsSync, statSync, mkdirSync } from 'fs';
import path from 'path';
import { z } from 'zod';
import { BaseTool, TOOL_CATEGORIES } from './tools.js';
import { FileSystemError } from '../utils/errors.js';
const WriteFileParams = z.object({
    path: z.string().describe('Path to the file to write'),
    content: z.string().describe('Content to write to the file'),
    encoding: z.enum(['utf8', 'base64', 'hex']).optional().default('utf8').describe('File encoding'),
    createDirectories: z.boolean().optional().default(true).describe('Create parent directories if they don\'t exist'),
    overwrite: z.boolean().optional().default(false).describe('Overwrite file if it exists'),
    backup: z.boolean().optional().default(false).describe('Create backup of existing file'),
});
export class WriteFileTool extends BaseTool {
    name = 'write_file';
    description = 'Write content to a file. Can create directories and backup existing files.';
    parameters = WriteFileParams;
    category = TOOL_CATEGORIES.FILE_SYSTEM;
    requiresApproval = true; // Writing files requires approval
    examples = [
        {
            description: 'Write text to a new file',
            parameters: {
                path: './output.txt',
                content: 'Hello, World!',
            },
        },
        {
            description: 'Write JSON data with directory creation',
            parameters: {
                path: './data/config.json',
                content: '{"key": "value"}',
                createDirectories: true,
            },
        },
        {
            description: 'Overwrite existing file with backup',
            parameters: {
                path: './existing-file.txt',
                content: 'New content',
                overwrite: true,
                backup: true,
            },
        },
    ];
    async execute(params, context) {
        const validatedParams = this.validateParams(params);
        const { path: filePath, content, encoding, createDirectories, overwrite, backup } = validatedParams;
        try {
            // Resolve the file path relative to working directory
            const resolvedPath = path.resolve(context.workingDirectory, filePath);
            // Security check: ensure the file is within the working directory
            if (!resolvedPath.startsWith(context.workingDirectory)) {
                return this.error('Access denied: File is outside the working directory');
            }
            // Check if file exists
            const fileExists = existsSync(resolvedPath);
            if (fileExists && !overwrite) {
                return this.error(`File already exists: ${filePath}. Use overwrite=true to replace it.`);
            }
            // Create backup if requested and file exists
            let backupPath;
            if (backup && fileExists) {
                const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
                backupPath = `${resolvedPath}.backup-${timestamp}`;
                try {
                    const originalContent = require('fs').readFileSync(resolvedPath);
                    writeFileSync(backupPath, originalContent);
                }
                catch (error) {
                    return this.error(`Failed to create backup: ${error instanceof Error ? error.message : String(error)}`);
                }
            }
            // Create parent directories if needed
            if (createDirectories) {
                const parentDir = path.dirname(resolvedPath);
                if (!existsSync(parentDir)) {
                    mkdirSync(parentDir, { recursive: true });
                }
            }
            // Write the file
            let bytesWritten;
            if (encoding === 'utf8') {
                writeFileSync(resolvedPath, content, 'utf8');
                bytesWritten = Buffer.byteLength(content, 'utf8');
            }
            else {
                const buffer = Buffer.from(content, encoding);
                writeFileSync(resolvedPath, buffer);
                bytesWritten = buffer.length;
            }
            // Get file stats
            const stats = statSync(resolvedPath);
            return this.success({
                path: filePath,
                resolvedPath,
                bytesWritten,
                size: stats.size,
                created: !fileExists,
                overwritten: fileExists && overwrite,
                backupCreated: !!backupPath,
                backupPath,
                encoding,
                lastModified: stats.mtime,
            });
        }
        catch (error) {
            if (error instanceof Error) {
                throw new FileSystemError(`Failed to write file: ${error.message}`, filePath);
            }
            throw error;
        }
    }
}
/**
 * Utility function to safely write JSON to a file
 */
export function writeJsonFile(filePath, data, workingDirectory) {
    const tool = new WriteFileTool();
    return tool.execute({
        path: filePath,
        content: JSON.stringify(data, null, 2),
        createDirectories: true,
    }, {
        workingDirectory,
        config: {},
        sessionId: 'utility-call'
    });
}
/**
 * Utility function to append content to a file
 */
export function appendToFile(filePath, content, workingDirectory) {
    const { readFileSync } = require('fs');
    const resolvedPath = path.resolve(workingDirectory, filePath);
    let existingContent = '';
    if (existsSync(resolvedPath)) {
        try {
            existingContent = readFileSync(resolvedPath, 'utf8');
        }
        catch (error) {
            // If we can't read the file, we'll just write the new content
        }
    }
    const tool = new WriteFileTool();
    return tool.execute({
        path: filePath,
        content: existingContent + content,
        overwrite: true,
        backup: existingContent.length > 0,
    }, {
        workingDirectory,
        config: {},
        sessionId: 'utility-call'
    });
}
