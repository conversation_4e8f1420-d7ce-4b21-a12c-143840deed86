import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */
import { useState, useEffect } from 'react';
import { Box, Text, useApp } from 'ink';
import { ChatInterface } from './ChatInterface.js';
import { ConfigCommand } from './commands/ConfigCommand.js';
import { ToolsCommand } from './commands/ToolsCommand.js';
import { MemoryCommand } from './commands/MemoryCommand.js';
import { AuthCommand } from './commands/AuthCommand.js';
import { LoadingSpinner } from './ui/LoadingSpinner.js';
import { ErrorDisplay } from './ui/ErrorDisplay.js';
import { ThemeProvider } from './ui/ThemeProvider.js';
export const ArienCLI = ({ config, args, telemetry, logger, }) => {
    const { exit } = useApp();
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState(null);
    const [initialized, setInitialized] = useState(false);
    // Initialize the CLI
    useEffect(() => {
        const initialize = async () => {
            try {
                logger.info('Initializing ARIEN CLI', { command: args.command }, 'ArienCLI');
                // Perform any async initialization here
                await new Promise(resolve => setTimeout(resolve, 100)); // Simulate initialization
                setInitialized(true);
                setIsLoading(false);
                logger.info('ARIEN CLI initialized successfully', {}, 'ArienCLI');
            }
            catch (err) {
                const errorMessage = err instanceof Error ? err.message : String(err);
                logger.error('Failed to initialize CLI', { error: errorMessage }, 'ArienCLI');
                setError(errorMessage);
                setIsLoading(false);
            }
        };
        initialize();
    }, [args.command, logger]);
    // Handle cleanup on exit
    useEffect(() => {
        return () => {
            logger.info('ARIEN CLI shutting down', {}, 'ArienCLI');
            telemetry.logEvent({
                name: 'cli.shutdown',
                properties: {
                    command: args.command,
                    duration: Date.now(), // This would be calculated properly in real implementation
                },
            });
        };
    }, [args.command, logger, telemetry]);
    // Show loading state
    if (isLoading) {
        return (_jsx(ThemeProvider, { config: config, children: _jsx(Box, { flexDirection: "column", padding: 1, children: _jsx(LoadingSpinner, { text: "Initializing ARIEN AI..." }) }) }));
    }
    // Show error state
    if (error) {
        return (_jsx(ThemeProvider, { config: config, children: _jsx(Box, { flexDirection: "column", padding: 1, children: _jsx(ErrorDisplay, { error: error, onRetry: () => {
                        setError(null);
                        setIsLoading(true);
                    } }) }) }));
    }
    // Render the appropriate command component
    const renderCommand = () => {
        switch (args.command) {
            case 'chat':
            case undefined:
                return (_jsx(ChatInterface, { config: config, args: args, telemetry: telemetry, logger: logger, onExit: () => exit() }));
            case 'config':
                return (_jsx(ConfigCommand, { config: config, args: args, telemetry: telemetry, logger: logger, onExit: () => exit() }));
            case 'tools':
                return (_jsx(ToolsCommand, { config: config, args: args, telemetry: telemetry, logger: logger, onExit: () => exit() }));
            case 'memory':
                return (_jsx(MemoryCommand, { config: config, args: args, telemetry: telemetry, logger: logger, onExit: () => exit() }));
            case 'auth':
                return (_jsx(AuthCommand, { config: config, args: args, telemetry: telemetry, logger: logger, onExit: () => exit() }));
            case 'version':
                return (_jsxs(Box, { flexDirection: "column", padding: 1, children: [_jsxs(Text, { color: "cyan", children: ["ARIEN AI CLI v", process.env.CLI_VERSION || '1.0.0'] }), _jsxs(Text, { color: "gray", children: ["Node.js ", process.version] }), _jsxs(Text, { color: "gray", children: ["Platform: ", process.platform, " ", process.arch] })] }));
            case 'help':
                return (_jsxs(Box, { flexDirection: "column", padding: 1, children: [_jsx(Text, { color: "cyan", bold: true, children: "ARIEN AI CLI - Your AI-powered development assistant" }), _jsx(Text, { children: " " }), _jsx(Text, { color: "yellow", children: "Usage:" }), _jsx(Text, { children: "  arien [options] [command]" }), _jsx(Text, { children: " " }), _jsx(Text, { color: "yellow", children: "Commands:" }), _jsx(Text, { children: "  chat      Start interactive chat session (default)" }), _jsx(Text, { children: "  config    Manage configuration" }), _jsx(Text, { children: "  tools     List available tools" }), _jsx(Text, { children: "  memory    Manage conversation memory" }), _jsx(Text, { children: "  auth      Manage authentication" }), _jsx(Text, { children: "  version   Show version information" }), _jsx(Text, { children: "  help      Show this help message" }), _jsx(Text, { children: " " }), _jsx(Text, { color: "yellow", children: "Options:" }), _jsx(Text, { children: "  -h, --help              Show help" }), _jsx(Text, { children: "  -v, --version           Show version" }), _jsx(Text, { children: "  -d, --debug             Enable debug mode" }), _jsx(Text, { children: "  -c, --config <path>     Configuration file" }), _jsx(Text, { children: "  --model <model>         AI model to use" }), _jsx(Text, { children: "  --approval-mode <mode>  Tool approval mode" })] }));
            default:
                return (_jsx(Box, { flexDirection: "column", padding: 1, children: _jsx(ErrorDisplay, { error: `Unknown command: ${args.command}`, onRetry: () => exit() }) }));
        }
    };
    return (_jsx(ThemeProvider, { config: config, children: _jsx(Box, { flexDirection: "column", minHeight: 3, children: renderCommand() }) }));
};
