{"name": "@arien/arien-ai-cli", "version": "1.0.0", "description": "ARIEN AI CLI - Advanced AI-powered command line interface for development workflows", "engines": {"node": ">=18.0.0"}, "type": "module", "workspaces": ["modules/*"], "repository": {"type": "git", "url": "https://github.com/arien-ai/arien-cli"}, "keywords": ["ai", "cli", "development", "assistant", "gemini", "automation"], "author": "ARIEN AI", "license": "Apache-2.0", "config": {"sandboxImageUri": "us-docker.pkg.dev/arien-code-dev/arien-cli/sandbox:1.0.0"}, "scripts": {"generate": "node scripts/generate-git-commit-info.js", "build": "node scripts/build.js", "build:sandbox": "node scripts/build-sandbox.js", "build:all": "npm run build && npm run build:sandbox", "clean": "node scripts/clean.js", "prepare": "npm run bundle", "test": "npm run test --workspaces", "test:ci": "npm run test:ci --workspaces --if-present", "test:e2e": "npm run test:integration:sandbox:none -- --verbose --keep-output", "test:integration:all": "npm run test:integration:sandbox:none && npm run test:integration:sandbox:docker && npm run test:integration:sandbox:podman", "test:integration:sandbox:none": "ARIEN_SANDBOX=false node integration-tests/run-tests.js", "test:integration:sandbox:docker": "ARIEN_SANDBOX=docker node integration-tests/run-tests.js", "test:integration:sandbox:podman": "ARIEN_SANDBOX=podman node integration-tests/run-tests.js", "start": "node scripts/start.js", "debug": "cross-env DEBUG=1 node --inspect-brk scripts/start.js", "lint:fix": "eslint . --fix && eslint integration-tests --fix", "lint": "eslint . --ext .ts,.tsx && eslint integration-tests", "lint:ci": "eslint . --ext .ts,.tsx --max-warnings 0 && eslint integration-tests --max-warnings 0", "typecheck": "npm run typecheck --workspaces --if-present", "format": "prettier --write .", "preflight": "npm run clean && npm ci && npm run format && npm run lint:ci && npm run build && npm run typecheck && npm run test:ci", "auth:npm": "npx google-artifactregistry-auth", "auth:docker": "gcloud auth configure-docker us-west1-docker.pkg.dev", "auth": "npm run auth:npm && npm run auth:docker", "prerelease:dev": "npm run prerelease:version --workspaces && npm run prerelease:deps --workspaces", "bundle": "npm run generate && node esbuild.config.js && node scripts/copy-bundle-assets.js", "build:cli": "npm run build --workspace modules/cli", "build:core": "npm run build --workspace modules/core", "build:modules": "npm run build:core && npm run build:cli", "build:sandbox:fast": "node scripts/build-sandbox.js --skip-npm-install-build", "prepare:cli-packagejson": "node scripts/prepare-cli-packagejson.js", "prepare:modules": "node scripts/prepare-cli-packagejson.js && node scripts/prepare-core-module.js", "publish:sandbox": "node scripts/publish-sandbox.js", "publish:npm": "npm publish --workspaces ${NPM_PUBLISH_TAG:+--tag=$NPM_PUBLISH_TAG} ${NPM_DRY_RUN:+--dry-run}", "publish:release": "npm run build:modules && npm run prepare:cli-packagejson && npm run build:sandbox:fast && npm run publish:sandbox && npm run publish:npm", "telemetry": "node scripts/telemetry.js", "start:gcp": "concurrently --raw --kill-others \"npm run telemetry -- --target=gcp\" \"npm start\"", "prepublishOnly": "node scripts/prepublish.js"}, "bin": {"arien": "bundle/arien.js"}, "files": ["bundle/", "README.md", "LICENSE", "docs/"], "devDependencies": {"@types/micromatch": "^4.0.9", "@types/mime-types": "^2.1.4", "@types/minimatch": "^5.1.2", "@vitest/coverage-v8": "^3.1.1", "concurrently": "^9.2.0", "cross-env": "^7.0.3", "esbuild": "^0.25.0", "eslint": "^9.24.0", "eslint-config-prettier": "^10.1.2", "eslint-plugin-import": "^2.31.0", "eslint-plugin-license-header": "^0.8.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "glob": "^10.4.5", "globals": "^16.0.0", "json": "^11.0.0", "lodash": "^4.17.21", "memfs": "^4.17.2", "prettier": "^3.5.3", "react-devtools-core": "^4.28.5", "typescript-eslint": "^8.30.1", "yargs": "^17.7.2"}, "dependencies": {}}