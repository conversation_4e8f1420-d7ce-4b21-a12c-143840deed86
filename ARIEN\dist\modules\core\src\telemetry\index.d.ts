/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */
export * from './types.js';
export * from './metrics.js';
export * from './loggers.js';
import { Config } from '../config/config.js';
import { TelemetryEvent, TelemetryMetric } from './types.js';
export declare class TelemetryManager {
    private logger;
    private metrics;
    private enabled;
    constructor(config: Config);
    /**
     * Log a telemetry event
     */
    logEvent(event: TelemetryEvent): void;
    /**
     * Record a metric
     */
    recordMetric(metric: TelemetryMetric): void;
    /**
     * Start timing an operation
     */
    startTimer(name: string): () => void;
    /**
     * Record a counter increment
     */
    incrementCounter(name: string, value?: number, tags?: Record<string, string>): void;
    /**
     * Record a gauge value
     */
    recordGauge(name: string, value: number, tags?: Record<string, string>): void;
    /**
     * Get collected metrics
     */
    getMetrics(): TelemetryMetric[];
    /**
     * Get logged events
     */
    getEvents(): TelemetryEvent[];
    /**
     * Clear all telemetry data
     */
    clear(): void;
    /**
     * Enable or disable telemetry
     */
    setEnabled(enabled: boolean): void;
    /**
     * Check if telemetry is enabled
     */
    isEnabled(): boolean;
    /**
     * Export telemetry data
     */
    export(): {
        events: TelemetryEvent[];
        metrics: TelemetryMetric[];
        timestamp: Date;
    };
}
/**
 * Get the global telemetry manager
 */
export declare function getTelemetry(): TelemetryManager | null;
/**
 * Initialize telemetry with configuration
 */
export declare function initTelemetry(config: Config): TelemetryManager;
/**
 * Convenience functions for common telemetry operations
 */
export declare function logEvent(event: TelemetryEvent): void;
export declare function recordMetric(metric: TelemetryMetric): void;
export declare function startTimer(name: string): () => void;
export declare function incrementCounter(name: string, value?: number, tags?: Record<string, string>): void;
export declare function recordGauge(name: string, value: number, tags?: Record<string, string>): void;
