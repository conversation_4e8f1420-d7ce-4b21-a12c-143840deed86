/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */

import path from 'path';
import os from 'os';
import { existsSync, mkdirSync } from 'fs';

/**
 * Get the ARIEN configuration directory
 */
export function getArienConfigDir(): string {
  const homeDir = os.homedir();
  return path.join(homeDir, '.arien');
}

/**
 * Get the ARIEN cache directory
 */
export function getArienCacheDir(): string {
  const configDir = getArienConfigDir();
  return path.join(configDir, 'cache');
}

/**
 * Get the ARIEN logs directory
 */
export function getArienLogsDir(): string {
  const configDir = getArienConfigDir();
  return path.join(configDir, 'logs');
}

/**
 * Get the ARIEN memory directory
 */
export function getArienMemoryDir(): string {
  const configDir = getArienConfigDir();
  return path.join(configDir, 'memory');
}

/**
 * Get the ARIEN extensions directory
 */
export function getArienExtensionsDir(): string {
  const configDir = getArienConfigDir();
  return path.join(configDir, 'extensions');
}

/**
 * Ensure a directory exists, creating it if necessary
 */
export function ensureDir(dirPath: string): void {
  if (!existsSync(dirPath)) {
    mkdirSync(dirPath, { recursive: true });
  }
}

/**
 * Ensure all ARIEN directories exist
 */
export function ensureArienDirs(): void {
  ensureDir(getArienConfigDir());
  ensureDir(getArienCacheDir());
  ensureDir(getArienLogsDir());
  ensureDir(getArienMemoryDir());
  ensureDir(getArienExtensionsDir());
}

/**
 * Resolve a path relative to the current working directory
 */
export function resolvePath(filePath: string, workingDir?: string): string {
  const baseDir = workingDir || process.cwd();
  return path.resolve(baseDir, filePath);
}

/**
 * Check if a path is within a given directory (security check)
 */
export function isPathWithinDirectory(filePath: string, directory: string): boolean {
  const resolvedPath = path.resolve(filePath);
  const resolvedDir = path.resolve(directory);
  return resolvedPath.startsWith(resolvedDir + path.sep) || resolvedPath === resolvedDir;
}

/**
 * Get a relative path from one directory to another
 */
export function getRelativePath(from: string, to: string): string {
  return path.relative(from, to);
}

/**
 * Normalize a file path for cross-platform compatibility
 */
export function normalizePath(filePath: string): string {
  return path.normalize(filePath).replace(/\\/g, '/');
}

/**
 * Get the file extension from a path
 */
export function getFileExtension(filePath: string): string {
  return path.extname(filePath).toLowerCase();
}

/**
 * Get the filename without extension
 */
export function getBaseName(filePath: string): string {
  return path.basename(filePath, path.extname(filePath));
}

/**
 * Join path segments safely
 */
export function joinPaths(...segments: string[]): string {
  return path.join(...segments);
}

/**
 * Check if a path is absolute
 */
export function isAbsolutePath(filePath: string): boolean {
  return path.isAbsolute(filePath);
}

/**
 * Convert a Windows path to Unix-style path
 */
export function toUnixPath(filePath: string): string {
  return filePath.replace(/\\/g, '/');
}

/**
 * Convert a Unix-style path to Windows path
 */
export function toWindowsPath(filePath: string): string {
  return filePath.replace(/\//g, '\\');
}

/**
 * Get the platform-appropriate path separator
 */
export function getPathSeparator(): string {
  return path.sep;
}

/**
 * Split a path into its components
 */
export function splitPath(filePath: string): string[] {
  return filePath.split(path.sep).filter(Boolean);
}

/**
 * Get the common base path of multiple paths
 */
export function getCommonBasePath(paths: string[]): string {
  if (paths.length === 0) return '';
  if (paths.length === 1) return path.dirname(paths[0]);

  const resolvedPaths = paths.map(p => path.resolve(p));
  const splitPaths = resolvedPaths.map(p => splitPath(p));
  
  let commonLength = 0;
  const minLength = Math.min(...splitPaths.map(p => p.length));
  
  for (let i = 0; i < minLength; i++) {
    const segment = splitPaths[0][i];
    if (splitPaths.every(p => p[i] === segment)) {
      commonLength++;
    } else {
      break;
    }
  }
  
  if (commonLength === 0) return '';
  
  return path.sep + splitPaths[0].slice(0, commonLength).join(path.sep);
}

/**
 * Check if a file path matches a glob pattern (simple implementation)
 */
export function matchesGlob(filePath: string, pattern: string): boolean {
  // Convert glob pattern to regex
  const regexPattern = pattern
    .replace(/\./g, '\\.')
    .replace(/\*/g, '.*')
    .replace(/\?/g, '.');
  
  const regex = new RegExp(`^${regexPattern}$`, 'i');
  return regex.test(filePath);
}

/**
 * Get a temporary file path
 */
export function getTempPath(prefix = 'arien', suffix = ''): string {
  const tempDir = os.tmpdir();
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2);
  return path.join(tempDir, `${prefix}-${timestamp}-${random}${suffix}`);
}
