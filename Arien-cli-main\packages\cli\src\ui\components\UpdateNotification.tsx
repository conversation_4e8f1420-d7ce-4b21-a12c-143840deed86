/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { Box, Text } from 'ink';
import { Colors } from '../colors.js';

interface UpdateNotificationProps {
  message: string;
}

export const UpdateNotification = ({ message }: UpdateNotificationProps) => (
  <Box
    borderStyle="round"
    borderColor={Colors.AccentYellow}
    paddingX={1}
    paddingY={0}
    marginY={1}
  >
    <Box flexDirection="row">
      <Text color={Colors.AccentYellow} bold>Update: </Text>
      <Text color={Colors.Foreground}>{message}</Text>
    </Box>
  </Box>
);
