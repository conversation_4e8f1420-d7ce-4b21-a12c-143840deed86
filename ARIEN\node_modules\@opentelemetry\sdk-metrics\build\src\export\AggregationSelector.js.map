{"version": 3, "file": "AggregationSelector.js", "sourceRoot": "", "sources": ["../../../src/export/AggregationSelector.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAGH,qDAAkD;AAClD,qEAAkE;AAgB3D,MAAM,4BAA4B,GACvC,eAAe,CAAC,EAAE,CAAC,yBAAW,CAAC,OAAO,EAAE,CAAC;AAD9B,QAAA,4BAA4B,gCACE;AACpC,MAAM,wCAAwC,GACnD,eAAe,CAAC,EAAE,CAAC,+CAAsB,CAAC,UAAU,CAAC;AAD1C,QAAA,wCAAwC,4CACE", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { InstrumentType } from '../InstrumentDescriptor';\nimport { Aggregation } from '../view/Aggregation';\nimport { AggregationTemporality } from './AggregationTemporality';\n\n/**\n * Aggregation selector based on metric instrument types.\n */\nexport type AggregationSelector = (\n  instrumentType: InstrumentType\n) => Aggregation;\n\n/**\n * Aggregation temporality selector based on metric instrument types.\n */\nexport type AggregationTemporalitySelector = (\n  instrumentType: InstrumentType\n) => AggregationTemporality;\n\nexport const DEFAULT_AGGREGATION_SELECTOR: AggregationSelector =\n  _instrumentType => Aggregation.Default();\nexport const DEFAULT_AGGREGATION_TEMPORALITY_SELECTOR: AggregationTemporalitySelector =\n  _instrumentType => AggregationTemporality.CUMULATIVE;\n"]}