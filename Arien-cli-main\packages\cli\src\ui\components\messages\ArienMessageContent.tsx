/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import React from 'react';
import { Box } from 'ink';
import { MarkdownDisplay } from '../../utils/MarkdownDisplay.js';

interface ArienMessageContentProps {
  text: string;
  isPending: boolean;
  availableTerminalHeight?: number;
  terminalWidth: number;
}

/*
 * Arien message content is a semi-hacked component. The intention is to represent a partial
 * of ArienMessage and is only used when a response gets too long. In that instance messages
 * are split into multiple ArienMessageContent's to enable the root <Static> component in
 * App.tsx to be as performant as humanly possible.
 */
export const ArienMessageContent: React.FC<ArienMessageContentProps> = ({
  text,
  isPending,
  availableTerminalHeight,
  terminalWidth,
}) => {
  // Calculate available width for content, accounting for paddingLeft of 2
  const contentWidth = Math.max(20, terminalWidth - 2);
  
  return (
    // Use the same spacing as ArienMessage for consistent alignment
    // This ensures content aligns properly without showing duplicate icons
    <Box flexDirection="column" paddingLeft={2}>
      <MarkdownDisplay
        text={text}
        isPending={isPending}
        availableTerminalHeight={availableTerminalHeight}
        terminalWidth={contentWidth}
      />
    </Box>
  );
};