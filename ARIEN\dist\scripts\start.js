/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */
import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';
import { existsSync } from 'fs';
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.dirname(__dirname);
/**
 * Start the ARIEN CLI
 */
async function start() {
    const bundlePath = path.join(rootDir, 'bundle', 'arien.js');
    // Check if bundle exists, if not build it
    if (!existsSync(bundlePath)) {
        console.log('📦 Bundle not found, building...');
        const buildProcess = spawn('npm', ['run', 'bundle'], {
            stdio: 'inherit',
            shell: true,
            cwd: rootDir,
        });
        await new Promise((resolve, reject) => {
            buildProcess.on('close', (code) => {
                if (code === 0) {
                    resolve();
                }
                else {
                    reject(new Error(`Build failed with code ${code}`));
                }
            });
        });
    }
    console.log('🚀 Starting ARIEN AI CLI...');
    // Start the CLI
    const cliProcess = spawn('node', [bundlePath, ...process.argv.slice(2)], {
        stdio: 'inherit',
        shell: true,
        cwd: process.cwd(),
    });
    cliProcess.on('close', (code) => {
        process.exit(code || 0);
    });
    // Handle process termination
    process.on('SIGINT', () => {
        cliProcess.kill('SIGINT');
    });
    process.on('SIGTERM', () => {
        cliProcess.kill('SIGTERM');
    });
}
// Run the start
start().catch((error) => {
    console.error('Start failed:', error);
    process.exit(1);
});
