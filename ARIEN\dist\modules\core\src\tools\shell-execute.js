/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */
import { exec, spawn } from 'child_process';
import { promisify } from 'util';
import { z } from 'zod';
import { BaseTool, TOOL_CATEGORIES } from './tools.js';
import { ArienError } from '../utils/errors.js';
const execAsync = promisify(exec);
const ShellExecuteParams = z.object({
    command: z.string().describe('Shell command to execute'),
    args: z.array(z.string()).optional().describe('Command arguments (alternative to including in command)'),
    cwd: z.string().optional().describe('Working directory for the command'),
    timeout: z.number().optional().default(30000).describe('Timeout in milliseconds'),
    env: z.record(z.string()).optional().describe('Environment variables'),
    shell: z.boolean().optional().default(true).describe('Run command in shell'),
    captureOutput: z.boolean().optional().default(true).describe('Capture stdout and stderr'),
    interactive: z.boolean().optional().default(false).describe('Run command interactively'),
});
export class ShellExecuteTool extends BaseTool {
    name = 'shell_execute';
    description = 'Execute shell commands and scripts. Use with caution as this can modify the system.';
    parameters = ShellExecuteParams;
    category = TOOL_CATEGORIES.SHELL;
    requiresApproval = true; // Shell execution always requires approval
    examples = [
        {
            description: 'List files in current directory',
            parameters: {
                command: 'ls -la',
            },
        },
        {
            description: 'Run npm install with timeout',
            parameters: {
                command: 'npm install',
                timeout: 60000,
                cwd: './project',
            },
        },
        {
            description: 'Execute with custom environment',
            parameters: {
                command: 'echo $CUSTOM_VAR',
                env: { CUSTOM_VAR: 'hello world' },
            },
        },
    ];
    async execute(params, context) {
        const validatedParams = this.validateParams(params);
        const { command, args, cwd, timeout, env, shell, captureOutput, interactive } = validatedParams;
        try {
            const workingDir = cwd ? cwd : context.workingDirectory;
            const startTime = Date.now();
            // Prepare environment variables
            const processEnv = {
                ...process.env,
                ...env,
            };
            let result;
            if (interactive) {
                // For interactive commands, we need to handle them differently
                result = await this.executeInteractive(command, args, workingDir, processEnv, timeout);
            }
            else if (args && args.length > 0) {
                // Use spawn for commands with separate arguments
                result = await this.executeWithSpawn(command, args, workingDir, processEnv, timeout, captureOutput);
            }
            else {
                // Use exec for simple shell commands
                result = await this.executeWithExec(command, workingDir, processEnv, timeout, shell);
            }
            const endTime = Date.now();
            const duration = endTime - startTime;
            return this.success({
                command: args ? `${command} ${args.join(' ')}` : command,
                exitCode: result.exitCode,
                stdout: result.stdout,
                stderr: result.stderr,
                signal: result.signal,
                duration,
                workingDirectory: workingDir,
                success: result.exitCode === 0,
            });
        }
        catch (error) {
            if (error instanceof Error) {
                throw new ArienError(`Shell execution failed: ${error.message}`, 'SHELL_EXECUTION_ERROR');
            }
            throw error;
        }
    }
    async executeWithExec(command, cwd, env, timeout, shell) {
        try {
            const execOptions = {
                cwd,
                env,
                timeout,
                maxBuffer: 1024 * 1024, // 1MB buffer
            };
            if (shell) {
                execOptions.shell = true;
            }
            const { stdout, stderr } = await execAsync(command, execOptions);
            return {
                stdout: stdout?.toString() || '',
                stderr: stderr?.toString() || '',
                exitCode: 0,
            };
        }
        catch (error) {
            return {
                stdout: error.stdout || '',
                stderr: error.stderr || error.message || '',
                exitCode: error.code || 1,
            };
        }
    }
    async executeWithSpawn(command, args, cwd, env, timeout, captureOutput) {
        return new Promise((resolve, reject) => {
            const child = spawn(command, args, {
                cwd,
                env,
                stdio: captureOutput ? 'pipe' : 'inherit',
            });
            let stdout = '';
            let stderr = '';
            let timeoutId;
            if (captureOutput) {
                child.stdout?.on('data', (data) => {
                    stdout += data.toString();
                });
                child.stderr?.on('data', (data) => {
                    stderr += data.toString();
                });
            }
            // Set timeout
            if (timeout > 0) {
                timeoutId = setTimeout(() => {
                    child.kill('SIGTERM');
                    reject(new Error(`Command timed out after ${timeout}ms`));
                }, timeout);
            }
            child.on('close', (code, signal) => {
                if (timeoutId)
                    clearTimeout(timeoutId);
                resolve({
                    stdout,
                    stderr,
                    exitCode: code || 0,
                    signal: signal || undefined,
                });
            });
            child.on('error', (error) => {
                if (timeoutId)
                    clearTimeout(timeoutId);
                reject(error);
            });
        });
    }
    async executeInteractive(command, args, cwd, env, timeout) {
        // For interactive commands, we'll run them non-interactively but with inherited stdio
        const fullCommand = args ? `${command} ${args.join(' ')}` : command;
        return new Promise((resolve, reject) => {
            const child = spawn(fullCommand, {
                cwd,
                env,
                stdio: 'inherit',
                shell: true,
            });
            let timeoutId;
            if (timeout > 0) {
                timeoutId = setTimeout(() => {
                    child.kill('SIGTERM');
                    reject(new Error(`Interactive command timed out after ${timeout}ms`));
                }, timeout);
            }
            child.on('close', (code) => {
                if (timeoutId)
                    clearTimeout(timeoutId);
                resolve({
                    stdout: '(interactive mode - output not captured)',
                    stderr: '',
                    exitCode: code || 0,
                });
            });
            child.on('error', (error) => {
                if (timeoutId)
                    clearTimeout(timeoutId);
                reject(error);
            });
        });
    }
    getRiskLevel() {
        return 'high'; // Shell execution is always high risk
    }
}
