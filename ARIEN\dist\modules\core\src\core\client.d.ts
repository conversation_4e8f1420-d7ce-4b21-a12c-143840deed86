/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */
import { Config } from '../config/config.js';
export interface ChatMessage {
    role: 'user' | 'model';
    parts: Array<{
        text?: string;
        inlineData?: {
            mimeType: string;
            data: string;
        };
        functionCall?: {
            name: string;
            args: Record<string, any>;
        };
        functionResponse?: {
            name: string;
            response: Record<string, any>;
        };
    }>;
}
export interface ChatOptions {
    temperature?: number;
    maxOutputTokens?: number;
    topP?: number;
    topK?: number;
    stopSequences?: string[];
}
export interface ToolDefinition {
    name: string;
    description: string;
    parameters: {
        type: 'object';
        properties: Record<string, any>;
        required?: string[];
    };
}
export declare class ArienClient {
    private genAI;
    private model;
    private config;
    private modelConfig;
    constructor(config: Config);
    private getApiKey;
    private getGenerationConfig;
    private convertToolsToFunctionDeclarations;
    /**
     * Generate content using the AI model
     */
    generateContent(prompt: string, options?: ChatOptions): Promise<string>;
    /**
     * Start a chat session
     */
    startChat(history?: ChatMessage[], tools?: ToolDefinition[]): Promise<ArienChatSession>;
    /**
     * Get model information
     */
    getModelInfo(): {
        name: string;
        displayName: string;
        maxTokens: number;
        supportsTools: boolean;
        supportsVision: boolean;
        supportsCodeExecution: boolean;
    };
    /**
     * Estimate token count for text
     */
    countTokens(text: string): Promise<number>;
}
export declare class ArienChatSession {
    private chat;
    private config;
    constructor(chat: any, config: Config);
    /**
     * Send a message in the chat session
     */
    sendMessage(message: string, _options?: ChatOptions): Promise<{
        text: string;
        functionCalls?: Array<{
            name: string;
            args: Record<string, any>;
        }>;
    }>;
    /**
     * Send a function response
     */
    sendFunctionResponse(functionName: string, response: Record<string, any>): Promise<string>;
    /**
     * Get chat history
     */
    getHistory(): ChatMessage[];
}
