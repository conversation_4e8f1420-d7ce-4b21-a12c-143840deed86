{"version": 3, "file": "read-file.js", "sourceRoot": "", "sources": ["../../src/tools/read-file.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,QAAQ,EAAE,MAAM,IAAI,CAAC;AACxD,OAAO,IAAI,MAAM,MAAM,CAAC;AACxB,OAAO,EAAE,CAAC,EAAE,MAAM,KAAK,CAAC;AACxB,OAAO,EAAE,QAAQ,EAA2B,eAAe,EAAE,MAAM,YAAY,CAAC;AAChF,OAAO,EAAE,eAAe,EAAE,MAAM,oBAAoB,CAAC;AAErD,MAAM,cAAc,GAAG,CAAC,CAAC,MAAM,CAAC;IAC9B,IAAI,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,0BAA0B,CAAC;IACrD,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,eAAe,CAAC;IAChG,OAAO,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC,QAAQ,CAAC,4BAA4B,CAAC;IAC1F,SAAS,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,wCAAwC,CAAC;IACnF,OAAO,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,qCAAqC,CAAC;CAC/E,CAAC,CAAC;AAEH,MAAM,OAAO,YAAa,SAAQ,QAAQ;IACxC,IAAI,GAAG,WAAW,CAAC;IACnB,WAAW,GAAG,sFAAsF,CAAC;IACrG,UAAU,GAAG,cAAc,CAAC;IACnB,QAAQ,GAAG,eAAe,CAAC,WAAW,CAAC;IACvC,gBAAgB,GAAG,KAAK,CAAC;IAEzB,QAAQ,GAAG;QAClB;YACE,WAAW,EAAE,2BAA2B;YACxC,UAAU,EAAE;gBACV,IAAI,EAAE,iBAAiB;aACxB;SACF;QACD;YACE,WAAW,EAAE,iCAAiC;YAC9C,UAAU,EAAE;gBACV,IAAI,EAAE,aAAa;gBACnB,SAAS,EAAE,CAAC;gBACZ,OAAO,EAAE,EAAE;aACZ;SACF;QACD;YACE,WAAW,EAAE,8BAA8B;YAC3C,UAAU,EAAE;gBACV,IAAI,EAAE,aAAa;gBACnB,QAAQ,EAAE,QAAQ;aACnB;SACF;KACF,CAAC;IAEF,KAAK,CAAC,OAAO,CAAC,MAAW,EAAE,OAAoB;QAC7C,MAAM,eAAe,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QACpD,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,eAAe,CAAC;QAElF,IAAI,CAAC;YACH,sDAAsD;YACtD,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,gBAAgB,EAAE,QAAQ,CAAC,CAAC;YAEtE,kEAAkE;YAClE,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,OAAO,CAAC,gBAAgB,CAAC,EAAE,CAAC;gBACvD,OAAO,IAAI,CAAC,KAAK,CAAC,sDAAsD,CAAC,CAAC;YAC5E,CAAC;YAED,uBAAuB;YACvB,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC;gBAC9B,OAAO,IAAI,CAAC,KAAK,CAAC,mBAAmB,QAAQ,EAAE,CAAC,CAAC;YACnD,CAAC;YAED,yCAAyC;YACzC,MAAM,KAAK,GAAG,QAAQ,CAAC,YAAY,CAAC,CAAC;YACrC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC;gBACpB,OAAO,IAAI,CAAC,KAAK,CAAC,uBAAuB,QAAQ,EAAE,CAAC,CAAC;YACvD,CAAC;YAED,kBAAkB;YAClB,IAAI,KAAK,CAAC,IAAI,GAAG,OAAO,EAAE,CAAC;gBACzB,OAAO,IAAI,CAAC,KAAK,CACf,mBAAmB,KAAK,CAAC,IAAI,gBAAgB,OAAO,SAAS,EAC7D,EAAE,QAAQ,EAAE,KAAK,CAAC,IAAI,EAAE,OAAO,EAAE,CAClC,CAAC;YACJ,CAAC;YAED,gBAAgB;YAChB,IAAI,OAAe,CAAC;YACpB,IAAI,QAAQ,KAAK,MAAM,EAAE,CAAC;gBACxB,OAAO,GAAG,YAAY,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;gBAE7C,iCAAiC;gBACjC,IAAI,SAAS,KAAK,SAAS,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;oBACrD,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;oBAClC,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,SAAS,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;oBAChD,MAAM,GAAG,GAAG,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC;oBAErE,IAAI,KAAK,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC;wBAC1B,OAAO,IAAI,CAAC,KAAK,CAAC,cAAc,SAAS,2BAA2B,KAAK,CAAC,MAAM,SAAS,CAAC,CAAC;oBAC7F,CAAC;oBAED,OAAO,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC/C,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,MAAM,MAAM,GAAG,YAAY,CAAC,YAAY,CAAC,CAAC;gBAC1C,OAAO,GAAG,MAAM,CAAC,QAAQ,CAAC,QAA0B,CAAC,CAAC;YACxD,CAAC;YAED,OAAO,IAAI,CAAC,OAAO,CAAC;gBAClB,OAAO;gBACP,IAAI,EAAE,QAAQ;gBACd,YAAY;gBACZ,QAAQ;gBACR,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,KAAK,EAAE,QAAQ,KAAK,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS;gBACnE,YAAY,EAAE,KAAK,CAAC,KAAK;aAC1B,CAAC,CAAC;QAEL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBAC3B,MAAM,IAAI,eAAe,CAAC,wBAAwB,KAAK,CAAC,OAAO,EAAE,EAAE,QAAQ,CAAC,CAAC;YAC/E,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;CACF;AAED;;GAEG;AACH,MAAM,UAAU,UAAU,CAAC,QAAgB;IACzC,MAAM,cAAc,GAAG;QACrB,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM;QAC7E,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM;QAC9E,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK;QAC3E,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM;QAC5E,MAAM,EAAE,aAAa,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO;QACxE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;QAC1E,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE,gBAAgB;KAC/E,CAAC;IAEF,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;IACjD,OAAO,cAAc,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;AACtC,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,WAAW,CAAC,QAAgB,EAAE,gBAAwB;IAQpE,IAAI,CAAC;QACH,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,QAAQ,CAAC,CAAC;QAE9D,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC;YAC9B,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC;QAC3B,CAAC;QAED,MAAM,KAAK,GAAG,QAAQ,CAAC,YAAY,CAAC,CAAC;QAErC,OAAO;YACL,MAAM,EAAE,IAAI;YACZ,MAAM,EAAE,KAAK,CAAC,MAAM,EAAE;YACtB,WAAW,EAAE,KAAK,CAAC,WAAW,EAAE;YAChC,IAAI,EAAE,KAAK,CAAC,IAAI;YAChB,YAAY,EAAE,KAAK,CAAC,KAAK;YACzB,MAAM,EAAE,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,SAAS;SAC9D,CAAC;IACJ,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC;IAC3B,CAAC;AACH,CAAC"}