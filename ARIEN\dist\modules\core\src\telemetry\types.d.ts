/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */
export interface TelemetryEvent {
    name: string;
    timestamp: Date;
    sessionId: string;
    userId?: string;
    properties?: Record<string, any>;
    tags?: Record<string, string>;
}
export interface TelemetryMetric {
    name: string;
    type: 'counter' | 'gauge' | 'histogram' | 'timer';
    value: number;
    timestamp: Date;
    sessionId: string;
    tags?: Record<string, string>;
    unit?: string;
}
export interface TimerMetric extends TelemetryMetric {
    type: 'timer';
    duration: number;
    startTime: Date;
    endTime: Date;
}
export interface CounterMetric extends TelemetryMetric {
    type: 'counter';
    increment: number;
}
export interface GaugeMetric extends TelemetryMetric {
    type: 'gauge';
    value: number;
}
export interface HistogramMetric extends TelemetryMetric {
    type: 'histogram';
    buckets: Record<string, number>;
    count: number;
    sum: number;
}
export declare const TELEMETRY_EVENTS: {
    readonly CLI_START: "cli.start";
    readonly CLI_EXIT: "cli.exit";
    readonly CLI_COMMAND: "cli.command";
    readonly CLI_ERROR: "cli.error";
    readonly CHAT_START: "chat.start";
    readonly CHAT_MESSAGE: "chat.message";
    readonly CHAT_RESPONSE: "chat.response";
    readonly CHAT_END: "chat.end";
    readonly TOOL_EXECUTE: "tool.execute";
    readonly TOOL_SUCCESS: "tool.success";
    readonly TOOL_ERROR: "tool.error";
    readonly TOOL_APPROVAL_REQUEST: "tool.approval.request";
    readonly TOOL_APPROVAL_GRANTED: "tool.approval.granted";
    readonly TOOL_APPROVAL_DENIED: "tool.approval.denied";
    readonly AI_REQUEST: "ai.request";
    readonly AI_RESPONSE: "ai.response";
    readonly AI_ERROR: "ai.error";
    readonly AI_TOKEN_USAGE: "ai.token.usage";
    readonly FILE_READ: "file.read";
    readonly FILE_WRITE: "file.write";
    readonly FILE_DELETE: "file.delete";
    readonly FILE_ERROR: "file.error";
    readonly AUTH_LOGIN: "auth.login";
    readonly AUTH_LOGOUT: "auth.logout";
    readonly AUTH_ERROR: "auth.error";
    readonly CONFIG_LOAD: "config.load";
    readonly CONFIG_UPDATE: "config.update";
    readonly CONFIG_ERROR: "config.error";
    readonly MEMORY_SAVE: "memory.save";
    readonly MEMORY_LOAD: "memory.load";
    readonly MEMORY_DELETE: "memory.delete";
    readonly MEMORY_ERROR: "memory.error";
    readonly EXTENSION_LOAD: "extension.load";
    readonly EXTENSION_UNLOAD: "extension.unload";
    readonly EXTENSION_ERROR: "extension.error";
};
export declare const TELEMETRY_METRICS: {
    readonly RESPONSE_TIME: "response.time";
    readonly TOKEN_COUNT: "token.count";
    readonly REQUEST_COUNT: "request.count";
    readonly ERROR_COUNT: "error.count";
    readonly TOOL_USAGE: "tool.usage";
    readonly COMMAND_USAGE: "command.usage";
    readonly MODEL_USAGE: "model.usage";
    readonly MEMORY_USAGE: "memory.usage";
    readonly CPU_USAGE: "cpu.usage";
    readonly DISK_USAGE: "disk.usage";
    readonly SESSION_DURATION: "session.duration";
    readonly MESSAGES_PER_SESSION: "session.messages";
    readonly TOOLS_PER_SESSION: "session.tools";
};
export type TelemetryEventName = typeof TELEMETRY_EVENTS[keyof typeof TELEMETRY_EVENTS];
export type TelemetryMetricName = typeof TELEMETRY_METRICS[keyof typeof TELEMETRY_METRICS];
