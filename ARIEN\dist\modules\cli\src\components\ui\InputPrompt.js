import { jsxs as _jsxs, jsx as _jsx } from "react/jsx-runtime";
/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */
import React, { useState } from 'react';
import { Box, Text, useInput } from 'ink';
import { useTheme } from './ThemeProvider.js';
export const InputPrompt = ({ onSubmit, placeholder = 'Type your message...', disabled = false, multiline = false, maxLength = 1000, }) => {
    const theme = useTheme();
    const [input, setInput] = useState('');
    const [cursorVisible, setCursorVisible] = useState(true);
    // Cursor blinking effect
    React.useEffect(() => {
        const interval = setInterval(() => {
            setCursorVisible(prev => !prev);
        }, 500);
        return () => clearInterval(interval);
    }, []);
    useInput((inputChar, key) => {
        if (disabled)
            return;
        if (key.return) {
            if (input.trim()) {
                onSubmit(input.trim());
                setInput('');
            }
            return;
        }
        if (key.backspace || key.delete) {
            setInput(prev => prev.slice(0, -1));
            return;
        }
        if (key.ctrl && inputChar === 'u') {
            // Clear line
            setInput('');
            return;
        }
        if (key.ctrl && inputChar === 'w') {
            // Delete word
            setInput(prev => {
                const words = prev.split(' ');
                words.pop();
                return words.join(' ');
            });
            return;
        }
        // Add character if printable and within length limit
        if (inputChar && !key.ctrl && !key.meta && input.length < maxLength) {
            setInput(prev => prev + inputChar);
        }
    });
    const displayText = input || placeholder;
    const isPlaceholder = !input;
    const cursor = cursorVisible && !disabled ? '│' : ' ';
    return (_jsxs(Box, { borderStyle: "single", borderColor: theme.colors.border, padding: 1, children: [_jsxs(Text, { color: theme.colors.primary, children: [theme.symbols.arrow, " "] }), _jsx(Text, { color: isPlaceholder ? theme.colors.textSecondary : theme.colors.text, children: displayText }), !disabled && (_jsx(Text, { color: theme.colors.primary, children: cursor })), disabled && (_jsx(Text, { color: theme.colors.textSecondary, children: " (disabled)" }))] }));
};
