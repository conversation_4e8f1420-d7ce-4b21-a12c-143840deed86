/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */
import { rmSync, existsSync } from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.dirname(__dirname);
/**
 * Clean all build artifacts
 */
function clean() {
    console.log('🧹 Cleaning build artifacts...');
    const pathsToClean = [
        path.join(rootDir, 'dist'),
        path.join(rootDir, 'bundle'),
        path.join(rootDir, 'coverage'),
        path.join(rootDir, 'modules/core/dist'),
        path.join(rootDir, 'modules/cli/dist'),
        path.join(rootDir, 'modules/core/coverage'),
        path.join(rootDir, 'modules/cli/coverage'),
    ];
    for (const cleanPath of pathsToClean) {
        if (existsSync(cleanPath)) {
            console.log(`  Removing: ${path.relative(rootDir, cleanPath)}`);
            rmSync(cleanPath, { recursive: true, force: true });
        }
    }
    console.log('✅ Clean completed');
}
// Run the clean
clean();
