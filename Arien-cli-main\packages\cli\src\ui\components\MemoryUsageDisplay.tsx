/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import React, { useEffect, useState } from 'react';
import { Box, Text } from 'ink';
import { Colors } from '../colors.js';
import process from 'node:process';
import { formatMemoryUsage } from '../utils/formatters.js';

export const MemoryUsageDisplay: React.FC = () => {
  const [memoryUsage, setMemoryUsage] = useState<string>('');
  const [memoryUsageColor, setMemoryUsageColor] = useState<string>(Colors.Gray);
  const [memoryIcon, setMemoryIcon] = useState<string>('');

  useEffect(() => {
    const updateMemory = () => {
      const usage = process.memoryUsage().rss;
      const usageGB = usage / (1024 * 1024 * 1024);
      
      setMemoryUsage(formatMemoryUsage(usage));
      
      // Enhanced color and icon based on usage levels
      if (usageGB >= 2) {
        setMemoryUsageColor(Colors.AccentRed);
        setMemoryIcon('▲');
      } else if (usageGB >= 1) {
        setMemoryUsageColor(Colors.AccentYellow);
        setMemoryIcon('▶');
      } else {
        setMemoryUsageColor(Colors.Gray);
        setMemoryIcon('●');
      }
    };
    const intervalId = setInterval(updateMemory, 2000);
    updateMemory(); // Initial update
    return () => clearInterval(intervalId);
  }, []);

  return (
    <Box>
      <Text color={Colors.Gray}>| </Text>
      <Text color={memoryUsageColor}>
        {memoryIcon} {memoryUsage}
      </Text>
    </Box>
  );
};
