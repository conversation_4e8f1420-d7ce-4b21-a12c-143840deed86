/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import React, { useEffect, useState } from 'react';
import { Text } from 'ink';
import { Colors } from '../../colors.js';

interface PromptIndicatorProps {
  type?: 'default' | 'shell' | 'completion' | 'waiting';
  animated?: boolean;
  color?: string;
}

const indicators = {
  default: ['>', '→', '⟶', '→', '>'],
  shell: ['!', '‼', '⁉', '‼', '!'],
  completion: ['.', '..', '...', '..', '.'],
  waiting: ['⊙', '◉', '◎', '◉', '⊙'],
};

export const PromptIndicator: React.FC<PromptIndicatorProps> = ({
  type = 'default',
  animated = true,
  color,
}) => {
  const [frame, setFrame] = useState(0);
  const sequence = indicators[type];
  
  useEffect(() => {
    if (!animated) return;
    
    const interval = setInterval(() => {
      setFrame((prev) => (prev + 1) % sequence.length);
    }, 300);
    
    return () => clearInterval(interval);
  }, [animated, sequence.length]);
  
  const indicator = animated ? sequence[frame] : sequence[0];
  const displayColor = color || (type === 'shell' ? Colors.AccentYellow : Colors.AccentPurple);
  
  return <Text color={displayColor} bold>{indicator} </Text>;
};

interface TypewriterTextProps {
  text: string;
  speed?: number;
  color?: string;
  onComplete?: () => void;
}

export const TypewriterText: React.FC<TypewriterTextProps> = ({
  text,
  speed = 50,
  color = Colors.Foreground,
  onComplete,
}) => {
  const [displayedText, setDisplayedText] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);
  
  useEffect(() => {
    if (currentIndex >= text.length) {
      onComplete?.();
      return;
    }
    
    const timeout = setTimeout(() => {
      setDisplayedText(text.slice(0, currentIndex + 1));
      setCurrentIndex(currentIndex + 1);
    }, speed);
    
    return () => clearTimeout(timeout);
  }, [currentIndex, text, speed, onComplete]);
  
  return <Text color={color}>{displayedText}</Text>;
}; 