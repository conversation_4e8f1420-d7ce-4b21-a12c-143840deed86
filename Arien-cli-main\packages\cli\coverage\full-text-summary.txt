------------------------------|---------|----------|---------|---------|---------------------------------
File                          | % Stmts | % Branch | % Funcs | % Lines | Uncovered Line #s               
------------------------------|---------|----------|---------|---------|---------------------------------
All files                     |   18.29 |    39.05 |   16.08 |   18.29 |                                 
 src                          |       0 |        0 |       0 |       0 |                                 
  arien.tsx                   |       0 |        0 |       0 |       0 | 1-296                           
  nonInteractiveCli.ts        |       0 |        0 |       0 |       0 | 1-156                           
 src/config                   |       0 |        0 |       0 |       0 |                                 
  auth.ts                     |       0 |        0 |       0 |       0 | 1-39                            
  config.ts                   |       0 |        0 |       0 |       0 | 1-302                           
  extension.ts                |       0 |        0 |       0 |       0 | 1-115                           
  sandboxConfig.ts            |       0 |        0 |       0 |       0 | 1-107                           
  settings.ts                 |       0 |        0 |       0 |       0 | 1-264                           
 src/generated                |       0 |        0 |       0 |       0 |                                 
  git-commit.ts               |       0 |        0 |       0 |       0 | 1-9                             
 src/ui                       |    6.96 |    84.61 |   46.66 |    6.96 |                                 
  App.tsx                     |       0 |        0 |       0 |       0 | 1-885                           
  colors.ts                   |   72.09 |      100 |   53.84 |   72.09 | ...8-19,21-22,30-31,42-43,48-49 
  constants.ts                |       0 |        0 |       0 |       0 | 1-15                            
  types.ts                    |     100 |      100 |     100 |     100 |                                 
 src/ui/components            |       0 |        0 |       0 |       0 |                                 
  AboutBox.tsx                |       0 |        0 |       0 |       0 | 1-122                           
  ArienRespondingSpinner.tsx  |       0 |        0 |       0 |       0 | 1-37                            
  AsciiArt.ts                 |       0 |        0 |       0 |       0 | 1-36                            
  AuthDialog.tsx              |       0 |        0 |       0 |       0 | 1-107                           
  AuthInProgress.tsx          |       0 |        0 |       0 |       0 | 1-57                            
  AutoAcceptIndicator.tsx     |       0 |        0 |       0 |       0 | 1-50                            
  ConsolePatcher.tsx          |       0 |        0 |       0 |       0 | 1-60                            
  ConsoleSummaryDisplay.tsx   |       0 |        0 |       0 |       0 | 1-35                            
  ContextSummaryDisplay.tsx   |       0 |        0 |       0 |       0 | 1-70                            
  DetailedMessagesDisplay.tsx |       0 |        0 |       0 |       0 | 1-82                            
  EditorSettingsDialog.tsx    |       0 |        0 |       0 |       0 | 1-168                           
  Footer.tsx                  |       0 |        0 |       0 |       0 | 1-128                           
  Header.tsx                  |       0 |        0 |       0 |       0 | 1-59                            
  Help.tsx                    |       0 |        0 |       0 |       0 | 1-172                           
  HistoryItemDisplay.tsx      |       0 |        0 |       0 |       0 | 1-95                            
  InputPrompt.tsx             |       0 |        0 |       0 |       0 | 1-473                           
  LoadingIndicator.tsx        |       0 |        0 |       0 |       0 | 1-97                            
  MemoryUsageDisplay.tsx      |       0 |        0 |       0 |       0 | 1-50                            
  SessionSummaryDisplay.tsx   |       0 |        0 |       0 |       0 | 1-88                            
  ShellModeIndicator.tsx      |       0 |        0 |       0 |       0 | 1-17                            
  ShowMoreLines.tsx           |       0 |        0 |       0 |       0 | 1-45                            
  Stats.tsx                   |       0 |        0 |       0 |       0 | 1-117                           
  StatsDisplay.tsx            |       0 |        0 |       0 |       0 | 1-97                            
  SuggestionsDisplay.tsx      |       0 |        0 |       0 |       0 | 1-108                           
  ThemeDialog.tsx             |       0 |        0 |       0 |       0 | 1-258                           
  Tips.tsx                    |       0 |        0 |       0 |       0 | 1-51                            
  UpdateNotification.tsx      |       0 |        0 |       0 |       0 | 1-26                            
 src/ui/components/messages   |   22.66 |       74 |    9.09 |   22.66 |                                 
  ArienMessage.tsx            |       0 |        0 |       0 |       0 | 1-45                            
  ArienMessageContent.tsx     |       0 |        0 |       0 |       0 | 1-45                            
  CompressionMessage.tsx      |       0 |        0 |       0 |       0 | 1-50                            
  DiffRenderer.tsx            |       0 |        0 |       0 |       0 | 1-312                           
  ErrorMessage.tsx            |       0 |        0 |       0 |       0 | 1-49                            
  InfoMessage.tsx             |       0 |        0 |       0 |       0 | 1-40                            
  ToolConfirmationMessage.tsx |       0 |        0 |       0 |       0 | 1-250                           
  ToolGroupMessage.tsx        |       0 |        0 |       0 |       0 | 1-123                           
  ToolMessage.tsx             |   97.54 |     92.5 |     100 |   97.54 | 96-97,225-227                   
  UserMessage.tsx             |       0 |        0 |       0 |       0 | 1-31                            
  UserShellMessage.tsx        |       0 |        0 |       0 |       0 | 1-25                            
 src/ui/components/shared     |    9.76 |    33.82 |   41.17 |    9.76 |                                 
  AnimatedIcon.tsx            |       0 |        0 |       0 |       0 | 1-41                            
  MaxSizedBox.tsx             |   48.61 |    38.33 |   77.77 |   48.61 | ...,485,487-491,495-525,534-535 
  ProgressBar.tsx             |       0 |        0 |       0 |       0 | 1-78                            
  PromptIndicator.tsx         |       0 |        0 |       0 |       0 | 1-79                            
  RadioButtonSelect.tsx       |       0 |        0 |       0 |       0 | 1-142                           
  Separator.tsx               |       0 |        0 |       0 |       0 | 1-92                            
  StatusBadge.tsx             |       0 |        0 |       0 |       0 | 1-85                            
  index.ts                    |       0 |        0 |       0 |       0 | 1-13                            
  text-buffer.ts              |       0 |        0 |       0 |       0 | 1-1415                          
 src/ui/contexts              |    8.82 |       50 |      20 |    8.82 |                                 
  OverflowContext.tsx         |   21.42 |      100 |   33.33 |   21.42 | 33,39-87                        
  SessionContext.tsx          |       0 |        0 |       0 |       0 | 1-204                           
  StreamingContext.tsx        |   42.85 |      100 |       0 |   42.85 | 15-22                           
 src/ui/editors               |       0 |        0 |       0 |       0 |                                 
  editorSettingsManager.ts    |       0 |        0 |       0 |       0 | 1-69                            
 src/ui/hooks                 |       0 |        0 |       0 |       0 |                                 
  atCommandProcessor.ts       |       0 |        0 |       0 |       0 | 1-425                           
  shellCommandProcessor.ts    |       0 |        0 |       0 |       0 | 1-348                           
  slashCommandProcessor.ts    |       0 |        0 |       0 |       0 | 1-1098                          
  useArienStream.ts           |       0 |        0 |       0 |       0 | 1-821                           
  useAuthCommand.ts           |       0 |        0 |       0 |       0 | 1-87                            
  useAutoAcceptIndicator.ts   |       0 |        0 |       0 |       0 | 1-49                            
  useBracketedPaste.ts        |       0 |        0 |       0 |       0 | 1-37                            
  useCompletion.ts            |       0 |        0 |       0 |       0 | 1-449                           
  useConsoleMessages.ts       |       0 |        0 |       0 |       0 | 1-89                            
  useEditorSettings.ts        |       0 |        0 |       0 |       0 | 1-75                            
  useGitBranchName.ts         |       0 |        0 |       0 |       0 | 1-79                            
  useHistoryManager.ts        |       0 |        0 |       0 |       0 | 1-119                           
  useInputHistory.ts          |       0 |        0 |       0 |       0 | 1-111                           
  useKeypress.ts              |       0 |        0 |       0 |       0 | 1-104                           
  useLoadingIndicator.ts      |       0 |        0 |       0 |       0 | 1-57                            
  useLogger.ts                |       0 |        0 |       0 |       0 | 1-32                            
  usePhraseCycler.ts          |       0 |        0 |       0 |       0 | 1-200                           
  usePrivacySettings.ts       |       0 |        0 |       0 |       0 | 1-135                           
  useReactToolScheduler.ts    |       0 |        0 |       0 |       0 | 1-312                           
  useRefreshMemoryCommand.ts  |       0 |        0 |       0 |       0 | 1-7                             
  useShellHistory.ts          |       0 |        0 |       0 |       0 | 1-103                           
  useShowMemoryCommand.ts     |       0 |        0 |       0 |       0 | 1-75                            
  useStateAndRef.ts           |       0 |        0 |       0 |       0 | 1-36                            
  useTerminalSize.ts          |       0 |        0 |       0 |       0 | 1-48                            
  useThemeCommand.ts          |       0 |        0 |       0 |       0 | 1-116                           
  useTimer.ts                 |       0 |        0 |       0 |       0 | 1-65                            
 src/ui/privacy               |       0 |        0 |       0 |       0 |                                 
  ArienPrivacyNotice.tsx      |       0 |        0 |       0 |       0 | 1-58                            
  CloudFreePrivacyNotice.tsx  |       0 |        0 |       0 |       0 | 1-113                           
  CloudPaidPrivacyNotice.tsx  |       0 |        0 |       0 |       0 | 1-55                            
  PrivacyNotice.tsx           |       0 |        0 |       0 |       0 | 1-41                            
 src/ui/themes                |    97.9 |    90.47 |   63.63 |    97.9 |                                 
  ansi-light.ts               |     100 |      100 |     100 |     100 |                                 
  ansi.ts                     |     100 |      100 |     100 |     100 |                                 
  atom-one-dark.ts            |     100 |      100 |     100 |     100 |                                 
  ayu-light.ts                |     100 |      100 |     100 |     100 |                                 
  ayu.ts                      |     100 |      100 |     100 |     100 |                                 
  default-light.ts            |     100 |      100 |     100 |     100 |                                 
  default.ts                  |     100 |      100 |     100 |     100 |                                 
  dracula.ts                  |     100 |      100 |     100 |     100 |                                 
  github-dark.ts              |     100 |      100 |     100 |     100 |                                 
  github-light.ts             |     100 |      100 |     100 |     100 |                                 
  googlecode.ts               |     100 |      100 |     100 |     100 |                                 
  no-color.ts                 |     100 |      100 |     100 |     100 |                                 
  shades-of-purple.ts         |     100 |      100 |     100 |     100 |                                 
  theme-manager.ts            |    53.4 |       75 |      50 |    53.4 | 58-81,89-104,107-111,118-119    
  theme.ts                    |   97.67 |    94.11 |      80 |   97.67 | 279-280,304-307                 
  xcode.ts                    |     100 |      100 |     100 |     100 |                                 
 src/ui/utils                 |    0.78 |        0 |       0 |    0.78 |                                 
  CodeColorizer.tsx           |       0 |        0 |       0 |       0 | 1-184                           
  MarkdownDisplay.tsx         |       0 |        0 |       0 |       0 | 1-490                           
  commandUtils.ts             |       0 |        0 |       0 |       0 | 1-26                            
  errorParsing.ts             |       0 |        0 |       0 |       0 | 1-106                           
  formatters.ts               |       0 |        0 |       0 |       0 | 1-63                            
  markdownUtilities.ts        |       0 |        0 |       0 |       0 | 1-125                           
  textUtils.ts                |   18.18 |      100 |       0 |   18.18 | 13-18,28-47,58-59,62-63,67-69   
  updateCheck.ts              |       0 |        0 |       0 |       0 | 1-36                            
 src/utils                    |       0 |        0 |       0 |       0 |                                 
  cleanup.ts                  |       0 |        0 |       0 |       0 | 1-19                            
  package.ts                  |       0 |        0 |       0 |       0 | 1-36                            
  readStdin.ts                |       0 |        0 |       0 |       0 | 1-39                            
  sandbox.ts                  |       0 |        0 |       0 |       0 | 1-871                           
  startupWarnings.ts          |       0 |        0 |       0 |       0 | 1-40                            
  version.ts                  |       0 |        0 |       0 |       0 | 1-12                            
------------------------------|---------|----------|---------|---------|---------------------------------
