/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */
import { z } from 'zod';
import { BaseTool, Tool<PERSON>ontext, ToolResult } from './tools.js';
export declare class WriteFileTool extends BaseTool {
    name: string;
    description: string;
    parameters: z.ZodObject<{
        path: z.ZodString;
        content: z.ZodString;
        encoding: z.ZodDefault<z.ZodOptional<z.ZodEnum<["utf8", "base64", "hex"]>>>;
        createDirectories: z.ZodDefault<z.ZodOptional<z.ZodBoolean>>;
        overwrite: z.ZodDefault<z.ZodOptional<z.ZodBoolean>>;
        backup: z.Zod<PERSON>efault<z.ZodOptional<z.ZodBoolean>>;
    }, "strip", z.ZodType<PERSON>ny, {
        path: string;
        encoding: "utf8" | "base64" | "hex";
        content: string;
        createDirectories: boolean;
        overwrite: boolean;
        backup: boolean;
    }, {
        path: string;
        content: string;
        encoding?: "utf8" | "base64" | "hex" | undefined;
        createDirectories?: boolean | undefined;
        overwrite?: boolean | undefined;
        backup?: boolean | undefined;
    }>;
    category: "file-system";
    requiresApproval: boolean;
    examples: ({
        description: string;
        parameters: {
            path: string;
            content: string;
            createDirectories?: undefined;
            overwrite?: undefined;
            backup?: undefined;
        };
    } | {
        description: string;
        parameters: {
            path: string;
            content: string;
            createDirectories: boolean;
            overwrite?: undefined;
            backup?: undefined;
        };
    } | {
        description: string;
        parameters: {
            path: string;
            content: string;
            overwrite: boolean;
            backup: boolean;
            createDirectories?: undefined;
        };
    })[];
    execute(params: any, context: ToolContext): Promise<ToolResult>;
}
/**
 * Utility function to safely write JSON to a file
 */
export declare function writeJsonFile(filePath: string, data: any, workingDirectory: string): Promise<ToolResult>;
/**
 * Utility function to append content to a file
 */
export declare function appendToFile(filePath: string, content: string, workingDirectory: string): Promise<ToolResult>;
//# sourceMappingURL=write-file.d.ts.map