import { jsx as _jsx } from "react/jsx-runtime";
/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */
import { createContext, useContext } from 'react';
const defaultTheme = {
    colors: {
        primary: 'cyan',
        secondary: 'blue',
        accent: 'magenta',
        success: 'green',
        warning: 'yellow',
        error: 'red',
        info: 'blue',
        text: 'white',
        textSecondary: 'gray',
        background: 'black',
        border: 'gray',
        highlight: 'bgBlue',
    },
    symbols: {
        success: '✓',
        error: '✗',
        warning: '⚠',
        info: 'ℹ',
        loading: '⠋',
        bullet: '•',
        arrow: '→',
        check: '✓',
        cross: '✗',
        question: '?',
    },
    spacing: {
        small: 1,
        medium: 2,
        large: 3,
    },
};
const darkTheme = {
    ...defaultTheme,
    colors: {
        ...defaultTheme.colors,
        primary: 'cyan',
        secondary: 'blue',
        text: 'white',
        textSecondary: 'gray',
        background: 'black',
    },
};
const lightTheme = {
    ...defaultTheme,
    colors: {
        ...defaultTheme.colors,
        primary: 'blue',
        secondary: 'cyan',
        text: 'black',
        textSecondary: 'gray',
        background: 'white',
    },
};
const colorfulTheme = {
    ...defaultTheme,
    colors: {
        ...defaultTheme.colors,
        primary: 'magenta',
        secondary: 'cyan',
        accent: 'yellow',
        success: 'green',
        warning: 'yellow',
        error: 'red',
    },
};
const minimalTheme = {
    ...defaultTheme,
    colors: {
        primary: 'white',
        secondary: 'gray',
        accent: 'white',
        success: 'white',
        warning: 'white',
        error: 'white',
        info: 'white',
        text: 'white',
        textSecondary: 'gray',
        background: 'black',
        border: 'gray',
        highlight: 'inverse',
    },
    symbols: {
        success: '+',
        error: '-',
        warning: '!',
        info: 'i',
        loading: '.',
        bullet: '-',
        arrow: '>',
        check: '+',
        cross: '-',
        question: '?',
    },
};
const themes = {
    default: defaultTheme,
    dark: darkTheme,
    light: lightTheme,
    colorful: colorfulTheme,
    minimal: minimalTheme,
};
const ThemeContext = createContext(defaultTheme);
export const ThemeProvider = ({ config, children, }) => {
    // Get theme from config or use default
    const themeName = config.getTheme?.() || 'default';
    const theme = themes[themeName] || defaultTheme;
    // Override theme based on environment
    const finalTheme = {
        ...theme,
        // Disable colors if not supported
        colors: process.stdout.isTTY ? theme.colors : {
            ...theme.colors,
            primary: 'white',
            secondary: 'white',
            accent: 'white',
            success: 'white',
            warning: 'white',
            error: 'white',
            info: 'white',
        },
        // Use ASCII symbols if Unicode not supported
        symbols: supportsUnicode() ? theme.symbols : {
            success: '+',
            error: 'x',
            warning: '!',
            info: 'i',
            loading: '.',
            bullet: '*',
            arrow: '>',
            check: '+',
            cross: 'x',
            question: '?',
        },
    };
    return (_jsx(ThemeContext.Provider, { value: finalTheme, children: children }));
};
export const useTheme = () => {
    const context = useContext(ThemeContext);
    if (!context) {
        throw new Error('useTheme must be used within a ThemeProvider');
    }
    return context;
};
/**
 * Check if the terminal supports Unicode characters
 */
function supportsUnicode() {
    const { env } = process;
    return !!(env.LANG?.includes('UTF-8') ||
        env.LC_ALL?.includes('UTF-8') ||
        env.LC_CTYPE?.includes('UTF-8') ||
        process.platform === 'win32');
}
/**
 * Get available theme names
 */
export function getAvailableThemes() {
    return Object.keys(themes);
}
/**
 * Create a custom theme
 */
export function createTheme(customTheme) {
    return {
        ...defaultTheme,
        ...customTheme,
        colors: {
            ...defaultTheme.colors,
            ...customTheme.colors,
        },
        symbols: {
            ...defaultTheme.symbols,
            ...customTheme.symbols,
        },
        spacing: {
            ...defaultTheme.spacing,
            ...customTheme.spacing,
        },
    };
}
/**
 * Register a new theme
 */
export function registerTheme(name, theme) {
    themes[name] = theme;
}
