/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */
/**
 * Setup global error handling for the CLI
 */
export declare function setupErrorHandling(): void;
/**
 * Format error for display to user
 */
export declare function formatError(error: unknown): string;
/**
 * Format error with stack trace for debugging
 */
export declare function formatErrorWithStack(error: unknown): string;
/**
 * Check if error is a user-facing error that should be displayed nicely
 */
export declare function isUserError(error: unknown): boolean;
/**
 * Handle CLI command errors
 */
export declare function handleCommandError(error: unknown, command?: string): void;
/**
 * Create a safe async wrapper that handles errors
 */
export declare function safeAsync<T extends any[], R>(fn: (...args: T) => Promise<R>): (...args: T) => Promise<R | undefined>;
/**
 * Create a safe sync wrapper that handles errors
 */
export declare function safeSync<T extends any[], R>(fn: (...args: T) => R): (...args: T) => R | undefined;
/**
 * Exit codes for different error types
 */
export declare const EXIT_CODES: {
    readonly SUCCESS: 0;
    readonly GENERAL_ERROR: 1;
    readonly INVALID_USAGE: 2;
    readonly CONFIGURATION_ERROR: 3;
    readonly AUTHENTICATION_ERROR: 4;
    readonly NETWORK_ERROR: 5;
    readonly FILE_ERROR: 6;
    readonly PERMISSION_ERROR: 7;
    readonly TIMEOUT_ERROR: 8;
};
/**
 * Get appropriate exit code for an error
 */
export declare function getExitCode(error: unknown): number;
/**
 * Exit with appropriate code and message
 */
export declare function exitWithError(error: unknown, command?: string): never;
