/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */
export interface CliArgs {
    command?: string;
    subcommand?: string;
    help?: boolean;
    version?: boolean;
    debug?: boolean;
    config?: string;
    model?: string;
    approvalMode?: 'auto' | 'manual' | 'none';
    sandbox?: boolean;
    telemetry?: boolean;
    message?: string;
    file?: string;
    positional: string[];
    raw: string[];
}
/**
 * Parse command line arguments
 */
export declare function parseArgs(argv: string[]): CliArgs;
/**
 * Validate parsed arguments
 */
export declare function validateArgs(args: CliArgs): void;
/**
 * Convert args back to command line string (for logging/debugging)
 */
export declare function argsToString(args: CliArgs): string;
/**
 * Get help text for a specific command
 */
export declare function getCommandHelp(command: string): string;
