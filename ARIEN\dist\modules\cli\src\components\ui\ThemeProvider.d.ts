/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */
import React from 'react';
import { Config } from '@arien/arien-ai-core';
export interface Theme {
    colors: {
        primary: string;
        secondary: string;
        accent: string;
        success: string;
        warning: string;
        error: string;
        info: string;
        text: string;
        textSecondary: string;
        background: string;
        border: string;
        highlight: string;
    };
    symbols: {
        success: string;
        error: string;
        warning: string;
        info: string;
        loading: string;
        bullet: string;
        arrow: string;
        check: string;
        cross: string;
        question: string;
    };
    spacing: {
        small: number;
        medium: number;
        large: number;
    };
}
export interface ThemeProviderProps {
    config: Config;
    children: React.ReactNode;
}
export declare const ThemeProvider: React.FC<ThemeProviderProps>;
export declare const useTheme: () => Theme;
/**
 * Get available theme names
 */
export declare function getAvailableThemes(): string[];
/**
 * Create a custom theme
 */
export declare function createTheme(customTheme: Partial<Theme>): Theme;
/**
 * Register a new theme
 */
export declare function registerTheme(name: string, theme: Theme): void;
