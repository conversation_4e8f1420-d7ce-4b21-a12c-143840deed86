---
description: 
globs: 
alwaysApply: true
---
# Configuration and Documentation Guide

## Configuration Files

### Project Configuration
- **Root Package**: [package.json](mdc:package.json) - Main workspace and script configuration
- **NPM Configuration**: [.npmrc](mdc:.npmrc) - NPM registry and package settings
- **Git Configuration**: [.gitignore](mdc:.gitignore) and [.gitattributes](mdc:.gitattributes) - Git handling

### Build and Development
- **TypeScript Config**: [tsconfig.json](mdc:tsconfig.json) - TypeScript project settings
- **ESBuild Config**: [esbuild.config.js](mdc:esbuild.config.js) - Build system configuration
- **Makefile**: [Makefile](mdc:Makefile) - Build automation commands

### Code Quality Configuration
- **ESLint**: [eslint.config.js](mdc:eslint.config.js) - Comprehensive linting rules
- **Prettier**: [.prettierrc.json](mdc:.prettierrc.json) - Code formatting standards
- **Editor Config**: [.editorconfig](mdc:.editorconfig) - Cross-editor settings

## Documentation Structure

### Main Documentation Hub
- **Documentation Index**: [docs/index.md](mdc:docs/index.md) - Documentation entry point
- **Project README**: [README.md](mdc:README.md) - Project overview and quickstart
- **Arien Guide**: [ARIEN.md](mdc:ARIEN.md) - Core project information

### Architecture Documentation
- **System Architecture**: [docs/architecture.md](mdc:docs/architecture.md) - High-level system design
- **Deployment Guide**: [docs/deployment.md](mdc:docs/deployment.md) - Deployment strategies
- **Extension Integration**: [docs/extension.md](mdc:docs/extension.md) - IDE extension guide

### CLI Documentation
- **CLI Index**: [docs/cli/index.md](mdc:docs/cli/index.md) - CLI documentation hub
- **Commands Reference**: [docs/cli/commands.md](mdc:docs/cli/commands.md) - Available CLI commands
- **Configuration Guide**: [docs/cli/configuration.md](mdc:docs/cli/configuration.md) - CLI setup and config
- **Authentication**: [docs/cli/authentication.md](mdc:docs/cli/authentication.md) - Auth methods and setup
- **Themes Guide**: [docs/cli/themes.md](mdc:docs/cli/themes.md) - Theme customization
- **Token Caching**: [docs/cli/token-caching.md](mdc:docs/cli/token-caching.md) - Auth token management
- **Tutorials**: [docs/cli/tutorials.md](mdc:docs/cli/tutorials.md) - Step-by-step guides

### Core Documentation  
- **Core Index**: [docs/core/index.md](mdc:docs/core/index.md) - Core package documentation
- **Tools API**: [docs/core/tools-api.md](mdc:docs/core/tools-api.md) - Tool development guide

### Tools Documentation
- **Tools Index**: [docs/tools/index.md](mdc:docs/tools/index.md) - Tools system overview
- **File System**: [docs/tools/file-system.md](mdc:docs/tools/file-system.md) - File operation tools
- **Shell Commands**: [docs/tools/shell.md](mdc:docs/tools/shell.md) - Shell integration
- **Web Fetch**: [docs/tools/web-fetch.md](mdc:docs/tools/web-fetch.md) - HTTP request tools
- **Web Search**: [docs/tools/web-search.md](mdc:docs/tools/web-search.md) - Search integration
- **Multi-File Operations**: [docs/tools/multi-file.md](mdc:docs/tools/multi-file.md) - Batch file tools
- **Memory System**: [docs/tools/memory.md](mdc:docs/tools/memory.md) - Conversation memory
- **MCP Integration**: [docs/tools/mcp-server.md](mdc:docs/tools/mcp-server.md) - MCP server support

## Advanced Configuration Topics

### System Integration
- **Sandbox Configuration**: [docs/sandbox.md](mdc:docs/sandbox.md) - Sandboxing system
- **Checkpointing**: [docs/checkpointing.md](mdc:docs/checkpointing.md) - State persistence
- **Integration Testing**: [docs/integration-tests.md](mdc:docs/integration-tests.md) - Testing strategies

### Privacy and Legal
- **Terms and Privacy**: [docs/tos-privacy.md](mdc:docs/tos-privacy.md) - Legal information
- **Telemetry**: [docs/telemetry.md](mdc:docs/telemetry.md) - Analytics and monitoring

### Troubleshooting and Support
- **Troubleshooting Guide**: [docs/troubleshooting.md](mdc:docs/troubleshooting.md) - Common issues and solutions
- **Issue Resolution**: Step-by-step problem solving

## Visual Assets
- **Screenshots**: [docs/assets/](mdc:docs/assets) - Project screenshots and visuals
- **Arien Screenshot**: [docs/assets/arien-screenshot.png](mdc:docs/assets/arien-screenshot.png) - Main project screenshot
- **Theme Examples**: Various theme screenshot examples

## Package-Specific Configuration

### CLI Package Configuration
- **CLI Package**: [packages/cli/package.json](mdc:packages/cli/package.json) - CLI dependencies and scripts
- **CLI TypeScript**: [packages/cli/tsconfig.json](mdc:packages/cli/tsconfig.json) - CLI TypeScript settings
- **CLI Vitest**: [packages/cli/vitest.config.ts](mdc:packages/cli/vitest.config.ts) - CLI test configuration

### Core Package Configuration
- **Core Package**: [packages/core/package.json](mdc:packages/core/package.json) - Core dependencies and scripts
- **Core TypeScript**: [packages/core/tsconfig.json](mdc:packages/core/tsconfig.json) - Core TypeScript settings
- **Core Vitest**: [packages/core/vitest.config.ts](mdc:packages/core/vitest.config.ts) - Core test configuration
- **Test Setup**: [packages/core/test-setup.ts](mdc:packages/core/test-setup.ts) - Core test environment

## Development Environment

### IDE Configuration
- **VSCode Settings**: [.vscode/](mdc:.vscode) - VSCode workspace configuration
- **Editor Integration**: Editor-specific settings and extensions

### Environment Variables
- **API Keys**: ARIEN_API_KEY for authentication
- **Debug Settings**: DEBUG environment variable for development
- **Sandbox Settings**: ARIEN_SANDBOX for sandbox configuration

## Build Artifacts and Distribution

### Bundle Configuration
- **Bundle Directory**: [bundle/](mdc:bundle) - Distribution artifacts
- **Asset Copying**: [scripts/copy_bundle_assets.js](mdc:scripts/copy_bundle_assets.js) - Asset management
- **Binary Creation**: Main executable bundle generation

### Release Configuration
- **Version Management**: [scripts/bind_package_version.js](mdc:scripts/bind_package_version.js) - Version synchronization
- **Dependency Management**: [scripts/bind_package_dependencies.js](mdc:scripts/bind_package_dependencies.js) - Dependency binding
- **Publication**: [scripts/prepublish.js](mdc:scripts/prepublish.js) - Pre-publication validation

