/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */
import { TelemetryMetric } from './types.js';
export declare class MetricsCollector {
    private metrics;
    private counters;
    private gauges;
    private timers;
    private enabled;
    private maxMetrics;
    constructor(enabled?: boolean);
    /**
     * Record a metric
     */
    recordMetric(metric: TelemetryMetric): void;
    /**
     * Increment a counter
     */
    incrementCounter(name: string, value?: number, tags?: Record<string, string>): void;
    /**
     * Record a gauge value
     */
    recordGauge(name: string, value: number, tags?: Record<string, string>): void;
    /**
     * Start a timer
     */
    startTimer(name: string, tags?: Record<string, string>): () => void;
    /**
     * Record a histogram value
     */
    recordHistogram(name: string, value: number, buckets: number[], tags?: Record<string, string>): void;
    /**
     * Get all recorded metrics
     */
    getMetrics(): TelemetryMetric[];
    /**
     * Get metrics by type
     */
    getMetricsByType(type: TelemetryMetric['type']): TelemetryMetric[];
    /**
     * Get metrics by name
     */
    getMetricsByName(name: string): TelemetryMetric[];
    /**
     * Get current counter values
     */
    getCounters(): Map<string, number>;
    /**
     * Get current gauge values
     */
    getGauges(): Map<string, number>;
    /**
     * Get active timers
     */
    getActiveTimers(): string[];
    /**
     * Clear all metrics
     */
    clear(): void;
    /**
     * Enable or disable metrics collection
     */
    setEnabled(enabled: boolean): void;
    /**
     * Check if metrics collection is enabled
     */
    isEnabled(): boolean;
    /**
     * Set the maximum number of metrics to keep
     */
    setMaxMetrics(max: number): void;
    /**
     * Get metrics summary
     */
    getSummary(): {
        totalMetrics: number;
        counters: number;
        gauges: number;
        timers: number;
        histograms: number;
        activeTimers: number;
    };
    /**
     * Generate a unique key for a metric with tags
     */
    private getMetricKey;
}
