---
description: 
globs: 
alwaysApply: true
---
# Development Workflow Guide

## Build System

### Build Configuration
- **Main Build**: [esbuild.config.js](mdc:esbuild.config.js) - ESBuild configuration for bundling
- **Package Build Script**: [scripts/build_package.js](mdc:scripts/build_package.js) - Individual package building
- **Main Build Script**: [scripts/build.js](mdc:scripts/build.js) - Complete build orchestration

### Key NPM Scripts (from [package.json](mdc:package.json))

#### Development
- `npm run start` - Start development CLI
- `npm run debug` - Start with debugging enabled
- `npm run build` - Build all packages
- `npm run build:packages` - Build core and CLI packages specifically
- `npm run bundle` - Create distribution bundle

#### Testing
- `npm run test` - Run unit tests across workspaces
- `npm run test:ci` - CI test run with coverage
- `npm run test:e2e` - End-to-end tests
- `npm run test:integration:all` - Full integration test suite

#### Code Quality
- `npm run lint` - Run ESLint checks
- `npm run lint:fix` - Auto-fix linting issues
- `npm run format` - Format code with Prettier
- `npm run typecheck` - TypeScript type checking

#### Sandbox & Deployment
- `npm run build:sandbox` - Build sandbox container
- `npm run publish:sandbox` - Publish sandbox image
- `npm run publish:release` - Full release pipeline

## Development Setup

### Prerequisites
- Node.js >= 18.0.0 (specified in [package.json](mdc:package.json))
- npm workspaces support

### Getting Started
1. `npm ci` - Clean install dependencies
2. `npm run build` - Build packages
3. `npm run start` - Run development CLI

### Project Structure for Development
- **Source Code**: All TypeScript sources in `packages/*/src/`
- **Build Outputs**: Compiled JavaScript in `packages/*/dist/`
- **Bundle Output**: [bundle/](mdc:bundle) - Final distribution artifacts
- **Tests**: Unit tests alongside source, integration tests in [integration-tests/](mdc:integration-tests)

## Build Artifacts

### TypeScript Compilation
- Each package compiles TypeScript to JavaScript + source maps
- **CLI Build Output**: `packages/cli/dist/`
- **Core Build Output**: `packages/core/dist/`

### Bundle Creation
- Main bundle: [bundle/arien.js](mdc:bundle/arien.js) - Executable CLI
- Asset copying: [scripts/copy_bundle_assets.js](mdc:scripts/copy_bundle_assets.js)

## Development Scripts

### Utility Scripts (in [scripts/](mdc:scripts))
- **Version Binding**: [scripts/bind_package_version.js](mdc:scripts/bind_package_version.js)
- **Dependency Management**: [scripts/bind_package_dependencies.js](mdc:scripts/bind_package_dependencies.js)
- **Clean**: [scripts/clean.js](mdc:scripts/clean.js) - Remove build artifacts
- **Git Info**: [scripts/generate-git-commit-info.js](mdc:scripts/generate-git-commit-info.js)

### Package Management
- **Workspace**: npm workspaces for multi-package management
- **Publishing**: Automated with `npm run publish:release`
- **Pre-publish**: [scripts/prepublish.js](mdc:scripts/prepublish.js) - Validation before publishing

## Quality Assurance

### Linting & Formatting
- **ESLint Config**: [eslint.config.js](mdc:eslint.config.js) - Code quality rules
- **Prettier Config**: [.prettierrc.json](mdc:.prettierrc.json) - Code formatting
- **Custom Rules**: [eslint-rules/](mdc:eslint-rules) - Project-specific linting

### TypeScript Configuration
- **Root Config**: [tsconfig.json](mdc:tsconfig.json) - Main TypeScript settings
- **Package Configs**: Individual `tsconfig.json` in each package

