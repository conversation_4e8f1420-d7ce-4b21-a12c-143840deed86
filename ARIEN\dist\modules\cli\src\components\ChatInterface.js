import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */
import { useState, useEffect, useCallback } from 'react';
import { Box, Text, useInput, useApp } from 'ink';
import { ArienClient } from '@arien/arien-ai-core';
import { useTheme } from './ui/ThemeProvider.js';
import { LoadingSpinner } from './ui/LoadingSpinner.js';
import { ErrorDisplay } from './ui/ErrorDisplay.js';
import { MessageDisplay } from './ui/MessageDisplay.js';
import { InputPrompt } from './ui/InputPrompt.js';
export const ChatInterface = ({ config, args, telemetry, logger, onExit, }) => {
    const theme = useTheme();
    const { exit } = useApp();
    const [messages, setMessages] = useState([]);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState(null);
    const [client, setClient] = useState(null);
    const [inputMode, setInputMode] = useState('chat');
    const [showWelcome, setShowWelcome] = useState(true);
    // Initialize AI client
    useEffect(() => {
        const initializeClient = async () => {
            try {
                const aiClient = new ArienClient(config);
                setClient(aiClient);
                logger.info('AI client initialized', {}, 'ChatInterface');
            }
            catch (err) {
                const errorMessage = err instanceof Error ? err.message : String(err);
                logger.error('Failed to initialize AI client', { error: errorMessage }, 'ChatInterface');
                setError(errorMessage);
            }
        };
        initializeClient();
    }, [config, logger]);
    // Handle single message mode
    useEffect(() => {
        if (args.message && client) {
            handleSendMessage(args.message);
        }
    }, [args.message, client]);
    // Handle file input
    useEffect(() => {
        if (args.file && client) {
            // TODO: Implement file reading and sending
            logger.info('File input requested', { file: args.file }, 'ChatInterface');
        }
    }, [args.file, client, logger]);
    const handleSendMessage = useCallback(async (content) => {
        if (!client || !content.trim())
            return;
        const userMessage = {
            id: `user-${Date.now()}`,
            role: 'user',
            content: content.trim(),
            timestamp: new Date(),
        };
        setMessages(prev => [...prev, userMessage]);
        setIsLoading(true);
        setError(null);
        setShowWelcome(false);
        try {
            logger.info('Sending message to AI', { content: content.substring(0, 100) }, 'ChatInterface');
            telemetry.logEvent({
                name: 'chat.message',
                properties: {
                    messageLength: content.length,
                    messageType: 'user',
                },
            });
            // TODO: Implement actual AI chat
            // For now, simulate a response
            await new Promise(resolve => setTimeout(resolve, 1000));
            const assistantMessage = {
                id: `assistant-${Date.now()}`,
                role: 'assistant',
                content: `I received your message: "${content}". This is a placeholder response while the AI integration is being completed.`,
                timestamp: new Date(),
            };
            setMessages(prev => [...prev, assistantMessage]);
            telemetry.logEvent({
                name: 'chat.response',
                properties: {
                    responseLength: assistantMessage.content.length,
                    messageType: 'assistant',
                },
            });
            // Exit if in single message mode
            if (args.message) {
                setTimeout(() => {
                    onExit();
                }, 1000);
            }
        }
        catch (err) {
            const errorMessage = err instanceof Error ? err.message : String(err);
            logger.error('Failed to send message', { error: errorMessage }, 'ChatInterface');
            setError(errorMessage);
            telemetry.logEvent({
                name: 'chat.error',
                properties: {
                    error: errorMessage,
                },
            });
        }
        finally {
            setIsLoading(false);
        }
    }, [client, logger, telemetry, args.message, onExit]);
    const handleCommand = useCallback((command) => {
        const cmd = command.toLowerCase().trim();
        switch (cmd) {
            case '/help':
                const helpMessage = {
                    id: `help-${Date.now()}`,
                    role: 'assistant',
                    content: `Available commands:
/help - Show this help message
/clear - Clear conversation history
/exit - Exit the chat
/debug - Toggle debug mode
/model - Show current AI model
/config - Show configuration`,
                    timestamp: new Date(),
                };
                setMessages(prev => [...prev, helpMessage]);
                break;
            case '/clear':
                setMessages([]);
                setShowWelcome(true);
                logger.info('Chat history cleared', {}, 'ChatInterface');
                break;
            case '/exit':
                onExit();
                break;
            case '/debug':
                logger.info('Debug info', {
                    messages: messages.length,
                    client: !!client,
                    config: config.getModel(),
                }, 'ChatInterface');
                break;
            case '/model':
                const modelMessage = {
                    id: `model-${Date.now()}`,
                    role: 'assistant',
                    content: `Current AI model: ${config.getModel()}`,
                    timestamp: new Date(),
                };
                setMessages(prev => [...prev, modelMessage]);
                break;
            case '/config':
                const configMessage = {
                    id: `config-${Date.now()}`,
                    role: 'assistant',
                    content: `Configuration:
Model: ${config.getModel()}
Approval Mode: ${config.getApprovalMode()}
Sandbox: ${config.isSandboxEnabled() ? 'Enabled' : 'Disabled'}
Telemetry: ${config.isTelemetryEnabled() ? 'Enabled' : 'Disabled'}`,
                    timestamp: new Date(),
                };
                setMessages(prev => [...prev, configMessage]);
                break;
            default:
                const unknownMessage = {
                    id: `unknown-${Date.now()}`,
                    role: 'assistant',
                    content: `Unknown command: ${command}. Type /help for available commands.`,
                    timestamp: new Date(),
                };
                setMessages(prev => [...prev, unknownMessage]);
                break;
        }
    }, [messages.length, client, config, logger, onExit]);
    const handleInput = useCallback((input) => {
        if (input.startsWith('/')) {
            handleCommand(input);
        }
        else {
            handleSendMessage(input);
        }
    }, [handleCommand, handleSendMessage]);
    // Handle keyboard shortcuts
    useInput((input, key) => {
        if (key.ctrl && input === 'c') {
            onExit();
        }
        if (key.ctrl && input === 'l') {
            setMessages([]);
            setShowWelcome(true);
        }
    });
    if (error && !client) {
        return (_jsx(ErrorDisplay, { error: error, details: "Failed to initialize AI client", onRetry: () => {
                setError(null);
                // Retry initialization
            } }));
    }
    return (_jsxs(Box, { flexDirection: "column", minHeight: 10, children: [_jsxs(Box, { borderStyle: "single", borderColor: theme.colors.border, padding: 1, children: [_jsx(Text, { color: theme.colors.primary, bold: true, children: "ARIEN AI Chat" }), _jsx(Text, { color: theme.colors.textSecondary, children: " - Type /help for commands, Ctrl+C to exit" })] }), showWelcome && messages.length === 0 && (_jsxs(Box, { padding: 1, children: [_jsxs(Text, { color: theme.colors.info, children: [theme.symbols.info, " Welcome to ARIEN AI! I'm your AI-powered development assistant."] }), _jsx(Text, { color: theme.colors.textSecondary, children: "Ask me anything about coding, debugging, or development tasks." })] })), _jsxs(Box, { flexDirection: "column", flexGrow: 1, padding: 1, children: [messages.map((message) => (_jsx(MessageDisplay, { message: message, theme: theme }, message.id))), isLoading && (_jsx(Box, { marginTop: 1, children: _jsx(LoadingSpinner, { text: "AI is thinking..." }) })), error && (_jsx(Box, { marginTop: 1, children: _jsx(ErrorDisplay, { error: error, showRetry: false }) }))] }), !args.message && (_jsx(InputPrompt, { onSubmit: handleInput, placeholder: "Type your message or /help for commands...", disabled: isLoading }))] }));
};
