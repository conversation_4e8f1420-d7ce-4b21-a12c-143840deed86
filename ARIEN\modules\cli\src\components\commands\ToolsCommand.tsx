/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */

import React, { useState, useEffect } from 'react';
import { Box, Text } from 'ink';
import { Config, TelemetryManager, Logger, getToolRegistry } from '@arien/arien-ai-core';
import { CliArgs } from '../../utils/args.js';
import { useTheme } from '../ui/ThemeProvider.js';
import { LoadingSpinner } from '../ui/LoadingSpinner.js';
import { ErrorDisplay } from '../ui/ErrorDisplay.js';

export interface ToolsCommandProps {
  config: Config;
  args: CliArgs;
  telemetry: TelemetryManager;
  logger: Logger;
  onExit: () => void;
}

export const ToolsCommand: React.FC<ToolsCommandProps> = ({
  config,
  args,
  telemetry,
  logger,
  onExit,
}) => {
  const theme = useTheme();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [result, setResult] = useState<string | null>(null);

  useEffect(() => {
    const executeCommand = async () => {
      try {
        setIsLoading(true);
        
        telemetry.logEvent({
          name: 'tools.command',
          properties: {
            subcommand: args.subcommand,
            args: args.positional,
          },
        });

        switch (args.subcommand) {
          case 'list':
            await handleList();
            break;
          case 'info':
            await handleInfo();
            break;
          case 'test':
            await handleTest();
            break;
          default:
            await handleList(); // Default to list
            break;
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : String(err);
        logger.error('Tools command failed', { error: errorMessage }, 'ToolsCommand');
        setError(errorMessage);
      } finally {
        setIsLoading(false);
      }
    };

    executeCommand();
  }, [args.subcommand, args.positional, logger, telemetry]);

  const handleList = async () => {
    const registry = getToolRegistry();
    const tools = registry.getAvailableTools();

    if (tools.length === 0) {
      setResult('No tools available');
      return;
    }

    const toolList = tools.map(tool => {
      const riskLevel = tool.getRiskLevel?.() || 'unknown';
      const category = tool.category || 'general';
      return `  ${tool.name} (${category}, ${riskLevel} risk) - ${tool.description}`;
    }).join('\n');

    setResult(`Available Tools (${tools.length}):
${toolList}`);
  };

  const handleInfo = async () => {
    const [toolName] = args.positional;
    if (!toolName) {
      throw new Error('Usage: arien tools info <tool-name>');
    }

    const registry = getToolRegistry();
    const tool = registry.getTool(toolName);

    if (!tool) {
      throw new Error(`Tool not found: ${toolName}`);
    }

    const info = `Tool Information: ${tool.name}
Description: ${tool.description}
Category: ${tool.category || 'general'}
Risk Level: ${tool.getRiskLevel?.() || 'unknown'}
Requires Approval: ${tool.requiresApproval ? 'Yes' : 'No'}

Parameters:
${tool.parameters ? JSON.stringify(tool.parameters, null, 2) : 'None'}

Examples:
${tool.examples?.map((example, i) => 
  `  ${i + 1}. ${example.description}\n     ${JSON.stringify(example.parameters, null, 6)}`
).join('\n') || 'None'}`;

    setResult(info);
  };

  const handleTest = async () => {
    const [toolName] = args.positional;
    if (!toolName) {
      throw new Error('Usage: arien tools test <tool-name>');
    }

    const registry = getToolRegistry();
    const tool = registry.getTool(toolName);

    if (!tool) {
      throw new Error(`Tool not found: ${toolName}`);
    }

    // TODO: Implement tool testing with sample parameters
    setResult(`Testing tool: ${toolName}
(Tool testing functionality would be implemented here)`);
  };

  // Auto-exit after showing result
  useEffect(() => {
    if (result && !isLoading) {
      const timer = setTimeout(() => {
        onExit();
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [result, isLoading, onExit]);

  if (isLoading) {
    return (
      <Box padding={1}>
        <LoadingSpinner text="Loading tools..." />
      </Box>
    );
  }

  if (error) {
    return (
      <Box padding={1}>
        <ErrorDisplay error={error} onRetry={() => setError(null)} />
      </Box>
    );
  }

  if (result) {
    return (
      <Box flexDirection="column" padding={1}>
        <Text color={theme.colors.info}>
          {theme.symbols.info} Tools
        </Text>
        <Box marginTop={1}>
          <Text>{result}</Text>
        </Box>
      </Box>
    );
  }

  return (
    <Box padding={1}>
      <Text color={theme.colors.textSecondary}>No result</Text>
    </Box>
  );
};
