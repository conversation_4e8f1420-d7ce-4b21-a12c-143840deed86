/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */
/**
 * Get the ARIEN configuration directory
 */
export declare function getArienConfigDir(): string;
/**
 * Get the ARIEN cache directory
 */
export declare function getArienCacheDir(): string;
/**
 * Get the ARIEN logs directory
 */
export declare function getArienLogsDir(): string;
/**
 * Get the ARIEN memory directory
 */
export declare function getArienMemoryDir(): string;
/**
 * Get the ARIEN extensions directory
 */
export declare function getArienExtensionsDir(): string;
/**
 * Ensure a directory exists, creating it if necessary
 */
export declare function ensureDir(dirPath: string): void;
/**
 * Ensure all ARIEN directories exist
 */
export declare function ensureArienDirs(): void;
/**
 * Resolve a path relative to the current working directory
 */
export declare function resolvePath(filePath: string, workingDir?: string): string;
/**
 * Check if a path is within a given directory (security check)
 */
export declare function isPathWithinDirectory(filePath: string, directory: string): boolean;
/**
 * Get a relative path from one directory to another
 */
export declare function getRelativePath(from: string, to: string): string;
/**
 * Normalize a file path for cross-platform compatibility
 */
export declare function normalizePath(filePath: string): string;
/**
 * Get the file extension from a path
 */
export declare function getFileExtension(filePath: string): string;
/**
 * Get the filename without extension
 */
export declare function getBaseName(filePath: string): string;
/**
 * Join path segments safely
 */
export declare function joinPaths(...segments: string[]): string;
/**
 * Check if a path is absolute
 */
export declare function isAbsolutePath(filePath: string): boolean;
/**
 * Convert a Windows path to Unix-style path
 */
export declare function toUnixPath(filePath: string): string;
/**
 * Convert a Unix-style path to Windows path
 */
export declare function toWindowsPath(filePath: string): string;
/**
 * Get the platform-appropriate path separator
 */
export declare function getPathSeparator(): string;
/**
 * Split a path into its components
 */
export declare function splitPath(filePath: string): string[];
/**
 * Get the common base path of multiple paths
 */
export declare function getCommonBasePath(paths: string[]): string;
/**
 * Check if a file path matches a glob pattern (simple implementation)
 */
export declare function matchesGlob(filePath: string, pattern: string): boolean;
/**
 * Get a temporary file path
 */
export declare function getTempPath(prefix?: string, suffix?: string): string;
//# sourceMappingURL=paths.d.ts.map