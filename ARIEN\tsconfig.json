{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "Node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "checkJs": false, "jsx": "react-jsx", "declaration": true, "outDir": "./dist", "rootDir": "./", "strict": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitThis": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "noImplicitOverride": true, "exactOptionalPropertyTypes": false, "noPropertyAccessFromIndexSignature": false, "noUnusedLocals": false, "noUnusedParameters": false, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "verbatimModuleSyntax": false, "lib": ["ES2022", "DOM"], "types": ["node"], "baseUrl": ".", "paths": {"@arien/core": ["./modules/core/src"], "@arien/core/*": ["./modules/core/src/*"], "@arien/cli": ["./modules/cli/src"], "@arien/cli/*": ["./modules/cli/src/*"]}}, "include": ["modules/**/*", "scripts/**/*", "integration-tests/**/*"], "exclude": ["node_modules", "dist", "bundle", "coverage", "**/*.test.ts", "**/*.test.tsx", "**/__tests__/**/*", "**/__mocks__/**/*"], "ts-node": {"esm": true, "experimentalSpecifierResolution": "node"}}