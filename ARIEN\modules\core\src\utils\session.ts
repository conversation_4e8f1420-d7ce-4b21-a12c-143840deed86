/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */

import { randomUUID } from 'crypto';

// Generate a unique session ID for this CLI session
export const sessionId = randomUUID();

// Track user prompts for telemetry and debugging
const userPrompts: Array<{
  timestamp: Date;
  prompt: string;
  sessionId: string;
}> = [];

/**
 * Log a user prompt for telemetry and debugging purposes
 */
export function logUserPrompt(prompt: string): void {
  userPrompts.push({
    timestamp: new Date(),
    prompt,
    sessionId,
  });
  
  // Keep only the last 100 prompts to avoid memory issues
  if (userPrompts.length > 100) {
    userPrompts.splice(0, userPrompts.length - 100);
  }
}

/**
 * Get all logged user prompts for this session
 */
export function getUserPrompts(): Array<{
  timestamp: Date;
  prompt: string;
  sessionId: string;
}> {
  return [...userPrompts];
}

/**
 * Clear all logged user prompts
 */
export function clearUserPrompts(): void {
  userPrompts.length = 0;
}

/**
 * Get session statistics
 */
export function getSessionStats(): {
  sessionId: string;
  startTime: Date;
  promptCount: number;
  uptime: number;
} {
  const startTime = userPrompts[0]?.timestamp || new Date();
  const uptime = Date.now() - startTime.getTime();
  
  return {
    sessionId,
    startTime,
    promptCount: userPrompts.length,
    uptime,
  };
}
