{"version": 3, "file": "system-check.js", "sourceRoot": "", "sources": ["../../src/utils/system-check.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,QAAQ,EAAE,MAAM,eAAe,CAAC;AAEzC,OAAO,EAAE,QAAQ,EAAE,IAAI,EAA0B,MAAM,IAAI,CAAC;AAC5D,OAAO,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAC;AAgBjD;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,uBAAuB;IAC3C,MAAM,MAAM,GAAG,SAAS,EAAE,CAAC;IAE3B,MAAM,UAAU,GAAe;QAC7B,QAAQ,EAAE,QAAQ,EAAE;QACpB,IAAI,EAAE,IAAI,EAAE;QACZ,WAAW,EAAE,OAAO,CAAC,OAAO;QAC5B,WAAW,EAAE,KAAK;QAClB,eAAe,EAAE;YACf,MAAM,EAAE,KAAK;YACb,OAAO,EAAE,KAAK;YACd,WAAW,EAAE,KAAK;SACnB;KACF,CAAC;IAEF,IAAI,CAAC;QACH,wBAAwB;QACxB,MAAM,gBAAgB,GAAG,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,uBAAuB,CAAC,CAAC;QACxE,IAAI,gBAAgB,EAAE,CAAC;YACrB,MAAM,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,GAAG,gBAAgB,CAAC;YAC1C,MAAM,YAAY,GAAG,QAAQ,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;YACzC,MAAM,YAAY,GAAG,QAAQ,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;YAEzC,IAAI,YAAY,GAAG,EAAE,EAAE,CAAC;gBACtB,MAAM,IAAI,KAAK,CAAC,mBAAmB,OAAO,CAAC,OAAO,2DAA2D,CAAC,CAAC;YACjH,CAAC;YAED,IAAI,YAAY,KAAK,EAAE,IAAI,YAAY,GAAG,CAAC,EAAE,CAAC;gBAC5C,OAAO,CAAC,IAAI,CAAC,oBAAoB,OAAO,CAAC,OAAO,wEAAwE,CAAC,CAAC;YAC5H,CAAC;QACH,CAAC;QAED,oBAAoB;QACpB,IAAI,CAAC;YACH,MAAM,gBAAgB,GAAG,QAAQ,CAAC,eAAe,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;YACxF,UAAU,CAAC,UAAU,GAAG,gBAAgB,CAAC,IAAI,EAAE,CAAC;QAClD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;QACjD,CAAC;QAED,oBAAoB;QACpB,IAAI,CAAC;YACH,MAAM,gBAAgB,GAAG,QAAQ,CAAC,eAAe,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;YACxF,MAAM,eAAe,GAAG,gBAAgB,CAAC,KAAK,CAAC,6BAA6B,CAAC,CAAC;YAC9E,IAAI,eAAe,EAAE,CAAC;gBACpB,UAAU,CAAC,UAAU,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;YAC7C,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;QACjD,CAAC;QAED,8BAA8B;QAC9B,UAAU,CAAC,WAAW,GAAG,MAAM,yBAAyB,EAAE,CAAC;QAE3D,yBAAyB;QACzB,UAAU,CAAC,eAAe,GAAG,oBAAoB,EAAE,CAAC;QAEpD,yBAAyB;QACzB,MAAM,CAAC,IAAI,CAAC,wBAAwB,EAAE,UAAU,CAAC,CAAC;QAElD,8BAA8B;QAC9B,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC;YAC5B,OAAO,CAAC,IAAI,CAAC,gFAAgF,CAAC,CAAC;QACjG,CAAC;QAED,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC;YACvC,OAAO,CAAC,IAAI,CAAC,qEAAqE,CAAC,CAAC;QACtF,CAAC;QAED,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,WAAW,EAAE,CAAC;YAC5C,OAAO,CAAC,IAAI,CAAC,qEAAqE,CAAC,CAAC;QACtF,CAAC;QAED,OAAO,UAAU,CAAC;IAEpB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,MAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;QAC/C,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,yBAAyB;IACtC,IAAI,CAAC;QACH,qCAAqC;QACrC,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,MAAM,CAAC,KAAK,CAAC,CAAC;QACvC,MAAM,EAAE,SAAS,EAAE,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,CAAC;QAC3C,MAAM,WAAW,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC;QAEtC,MAAM,WAAW,CAAC,YAAY,CAAC,CAAC;QAChC,OAAO,IAAI,CAAC;IACd,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC;AAED;;GAEG;AACH,SAAS,oBAAoB;IAC3B,MAAM,OAAO,GAAG;QACd,MAAM,EAAE,KAAK;QACb,OAAO,EAAE,KAAK;QACd,WAAW,EAAE,KAAK;KACnB,CAAC;IAEF,sBAAsB;IACtB,MAAM,EAAE,GAAG,EAAE,GAAG,OAAO,CAAC;IACxB,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CACjB,GAAG,CAAC,WAAW;QACf,GAAG,CAAC,SAAS;QACb,GAAG,CAAC,IAAI,KAAK,gBAAgB;QAC7B,GAAG,CAAC,IAAI,KAAK,iBAAiB;QAC9B,CAAC,GAAG,CAAC,IAAI,IAAI,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QACxC,OAAO,CAAC,MAAM,CAAC,KAAK,CACrB,CAAC;IAEF,wBAAwB;IACxB,OAAO,CAAC,OAAO,GAAG,CAAC,CAAC,CAClB,GAAG,CAAC,IAAI,EAAE,QAAQ,CAAC,OAAO,CAAC;QAC3B,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,OAAO,CAAC;QAC7B,GAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC,OAAO,CAAC;QAC/B,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,qCAAqC;KACnE,CAAC;IAEF,mCAAmC;IACnC,OAAO,CAAC,WAAW,GAAG,CAAC,CAAC,CACtB,OAAO,CAAC,KAAK,CAAC,KAAK;QACnB,OAAO,CAAC,MAAM,CAAC,KAAK;QACpB,CAAC,GAAG,CAAC,EAAE,IAAI,wBAAwB;QACnC,CAAC,GAAG,CAAC,eAAe,CACrB,CAAC;IAEF,OAAO,OAAO,CAAC;AACjB,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,yBAAyB;IACvC,MAAM,MAAM,GAAG,SAAS,EAAE,CAAC;IAC3B,MAAM,EAAE,eAAe,EAAE,iBAAiB,EAAE,GAAG,OAAO,CAAC,sBAAsB,CAAC,CAAC;IAE/E,IAAI,CAAC;QACH,iCAAiC;QACjC,eAAe,EAAE,CAAC;QAElB,yBAAyB;QACzB,MAAM,SAAS,GAAG,iBAAiB,EAAE,CAAC;QACtC,MAAM,QAAQ,GAAG,GAAG,SAAS,gBAAgB,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;QAE1D,IAAI,CAAC;YACH,MAAM,EAAE,aAAa,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;YACpD,aAAa,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YAChC,UAAU,CAAC,QAAQ,CAAC,CAAC;QACvB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,4CAA4C,SAAS,EAAE,CAAC,CAAC;QAC3E,CAAC;QAED,MAAM,CAAC,KAAK,CAAC,oCAAoC,CAAC,CAAC;IAErD,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,MAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;QAC9D,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,aAAa;IAC3B,MAAM,EAAE,GAAG,EAAE,GAAG,OAAO,CAAC;IAExB,OAAO;QACL,eAAe;QACf,WAAW,EAAE,OAAO,CAAC,OAAO;QAC5B,QAAQ,EAAE,OAAO,CAAC,QAAQ;QAC1B,IAAI,EAAE,OAAO,CAAC,IAAI;QAElB,mBAAmB;QACnB,KAAK,EAAE,GAAG,CAAC,KAAK,IAAI,GAAG,CAAC,OAAO;QAC/B,IAAI,EAAE,GAAG,CAAC,IAAI;QACd,SAAS,EAAE,GAAG,CAAC,SAAS;QACxB,IAAI,EAAE,GAAG,CAAC,IAAI;QAEd,gBAAgB;QAChB,KAAK,EAAE;YACL,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,KAAK;YAC1B,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC,KAAK;YAC5B,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC,KAAK;SAC7B;QAED,eAAe;QACf,EAAE,EAAE,CAAC,CAAC,CACJ,GAAG,CAAC,EAAE;YACN,GAAG,CAAC,sBAAsB;YAC1B,GAAG,CAAC,YAAY;YAChB,GAAG,CAAC,cAAc;YAClB,GAAG,CAAC,SAAS;YACb,GAAG,CAAC,QAAQ;YACZ,GAAG,CAAC,MAAM,CACX;QAED,cAAc;QACd,MAAM,EAAE,OAAO,CAAC,WAAW,EAAE;QAE7B,eAAe;QACf,GAAG,EAAE,OAAO,CAAC,GAAG;QAChB,IAAI,EAAE,OAAO,CAAC,IAAI;QAElB,cAAc;QACd,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE;KACzB,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,uBAAuB;IACrC,MAAM,MAAM,GAAG,SAAS,EAAE,CAAC;IAE3B,sDAAsD;IACtD,IAAI,OAAO,OAAO,KAAK,WAAW,IAAI,OAAO,OAAO,KAAK,WAAW,EAAE,CAAC;QACrE,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;IAC9D,CAAC;IAED,oEAAoE;IACpE,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE,CAAC;QAClC,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;IACnE,CAAC;IAED,mDAAmD;IACnD,MAAM,YAAY,GAAG,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,QAAQ,CAAC,CAAC;IACrE,KAAK,MAAM,GAAG,IAAI,YAAY,EAAE,CAAC;QAC/B,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,CAAC;QACf,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,yBAAyB,GAAG,oBAAoB,CAAC,CAAC;QACpE,CAAC;IACH,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,kCAAkC,CAAC,CAAC;AACnD,CAAC"}