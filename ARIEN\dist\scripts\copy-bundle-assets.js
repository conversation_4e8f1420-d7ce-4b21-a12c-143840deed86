/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */
import { copyFileSync, mkdirSync, existsSync, readdirSync, statSync } from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.dirname(__dirname);
/**
 * Copy bundle assets to the bundle directory
 */
function copyBundleAssets() {
    console.log('📁 Copying bundle assets...');
    const bundleDir = path.join(rootDir, 'bundle');
    // Ensure bundle directory exists
    mkdirSync(bundleDir, { recursive: true });
    // Copy sandbox files from CLI module
    const cliUtilsDir = path.join(rootDir, 'modules/cli/src/utils');
    const sandboxFiles = [
        'sandbox-macos-permissive-closed.sb',
        'sandbox-macos-permissive-open.sb',
        'sandbox-macos-permissive-proxied.sb',
        'sandbox-macos-restrictive-closed.sb',
        'sandbox-macos-restrictive-open.sb',
        'sandbox-macos-restrictive-proxied.sb',
    ];
    for (const file of sandboxFiles) {
        const srcPath = path.join(cliUtilsDir, file);
        const destPath = path.join(bundleDir, file);
        if (existsSync(srcPath)) {
            copyFileSync(srcPath, destPath);
            console.log(`  Copied: ${file}`);
        }
        else {
            console.warn(`  Warning: ${file} not found`);
        }
    }
    // Copy any additional assets
    const assetsDir = path.join(rootDir, 'assets');
    if (existsSync(assetsDir)) {
        copyDirectory(assetsDir, bundleDir);
    }
    console.log('✅ Bundle assets copied');
}
/**
 * Recursively copy a directory
 */
function copyDirectory(src, dest) {
    if (!existsSync(dest)) {
        mkdirSync(dest, { recursive: true });
    }
    const items = readdirSync(src);
    for (const item of items) {
        const srcPath = path.join(src, item);
        const destPath = path.join(dest, item);
        if (statSync(srcPath).isDirectory()) {
            copyDirectory(srcPath, destPath);
        }
        else {
            copyFileSync(srcPath, destPath);
            console.log(`  Copied: ${path.relative(rootDir, destPath)}`);
        }
    }
}
// Run the copy
copyBundleAssets();
