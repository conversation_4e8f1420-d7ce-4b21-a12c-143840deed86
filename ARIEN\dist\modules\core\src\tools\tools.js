/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */
import { z } from 'zod';
export class BaseTool {
    requiresApproval = false;
    category = 'general';
    examples = [];
    /**
     * Validate parameters against the schema
     */
    validateParams(params) {
        try {
            return this.parameters.parse(params);
        }
        catch (error) {
            if (error instanceof z.ZodError) {
                const issues = error.issues.map(issue => `${issue.path.join('.')}: ${issue.message}`).join(', ');
                throw new Error(`Parameter validation failed: ${issues}`);
            }
            throw error;
        }
    }
    /**
     * Create a successful result
     */
    success(data, metadata) {
        return {
            success: true,
            data,
            metadata,
        };
    }
    /**
     * Create an error result
     */
    error(message, metadata) {
        return {
            success: false,
            error: message,
            metadata,
        };
    }
    /**
     * Get the tool definition for AI model registration
     */
    getDefinition() {
        return {
            name: this.name,
            description: this.description,
            parameters: this.zodToJsonSchema(this.parameters),
        };
    }
    /**
     * Convert Zod schema to JSON Schema for AI model
     */
    zodToJsonSchema(schema) {
        // This is a simplified conversion - in a real implementation,
        // you'd want to use a library like zod-to-json-schema
        if (schema instanceof z.ZodObject) {
            const shape = schema.shape;
            const properties = {};
            const required = [];
            for (const [key, value] of Object.entries(shape)) {
                if (value instanceof z.ZodString) {
                    properties[key] = { type: 'string' };
                    if (!value.isOptional())
                        required.push(key);
                }
                else if (value instanceof z.ZodNumber) {
                    properties[key] = { type: 'number' };
                    if (!value.isOptional())
                        required.push(key);
                }
                else if (value instanceof z.ZodBoolean) {
                    properties[key] = { type: 'boolean' };
                    if (!value.isOptional())
                        required.push(key);
                }
                else if (value instanceof z.ZodArray) {
                    properties[key] = { type: 'array' };
                    if (!value.isOptional())
                        required.push(key);
                }
                else {
                    properties[key] = { type: 'object' };
                    if (!value.isOptional())
                        required.push(key);
                }
            }
            return {
                type: 'object',
                properties,
                required: required.length > 0 ? required : undefined,
            };
        }
        return {
            type: 'object',
            properties: {},
        };
    }
}
/**
 * Tool execution modes
 */
export var ToolExecutionMode;
(function (ToolExecutionMode) {
    ToolExecutionMode["AUTO"] = "auto";
    ToolExecutionMode["MANUAL"] = "manual";
    ToolExecutionMode["NONE"] = "none";
})(ToolExecutionMode || (ToolExecutionMode = {}));
/**
 * Tool categories for organization
 */
export const TOOL_CATEGORIES = {
    FILE_SYSTEM: 'file-system',
    SHELL: 'shell',
    WEB: 'web',
    MEMORY: 'memory',
    DEVELOPMENT: 'development',
    COMMUNICATION: 'communication',
    UTILITY: 'utility',
};
/**
 * Risk levels for tool execution
 */
export const RISK_LEVELS = {
    LOW: 'low', // Read-only operations, safe queries
    MEDIUM: 'medium', // File modifications, network requests
    HIGH: 'high', // System commands, destructive operations
};
/**
 * Utility function to estimate tool risk based on operation type
 */
export function estimateToolRisk(toolName, _parameters) {
    const name = toolName.toLowerCase();
    // High risk operations
    if (name.includes('delete') ||
        name.includes('remove') ||
        name.includes('shell') ||
        name.includes('exec') ||
        name.includes('kill')) {
        return RISK_LEVELS.HIGH;
    }
    // Medium risk operations
    if (name.includes('write') ||
        name.includes('edit') ||
        name.includes('create') ||
        name.includes('modify') ||
        name.includes('web')) {
        return RISK_LEVELS.MEDIUM;
    }
    // Default to low risk for read operations
    return RISK_LEVELS.LOW;
}
