/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */
export declare enum LogLevel {
    DEBUG = 0,
    INFO = 1,
    WARN = 2,
    ERROR = 3
}
export interface LogEntry {
    timestamp: Date;
    level: LogLevel;
    message: string;
    data?: any;
    sessionId: string;
    source?: string;
}
export declare class Logger {
    private logLevel;
    private logFile?;
    private enableConsole;
    private enableFile;
    private logBuffer;
    private maxBufferSize;
    constructor(options?: {
        level?: LogLevel;
        logFile?: string;
        enableConsole?: boolean;
        enableFile?: boolean;
        maxBufferSize?: number;
    });
    private ensureLogDirectory;
    private shouldLog;
    private formatMessage;
    private writeToConsole;
    private writeToFile;
    private addToBuffer;
    private log;
    debug(message: string, data?: any, source?: string): void;
    info(message: string, data?: any, source?: string): void;
    warn(message: string, data?: any, source?: string): void;
    error(message: string, data?: any, source?: string): void;
    /**
     * Set the log level
     */
    setLevel(level: LogLevel): void;
    /**
     * Get the current log level
     */
    getLevel(): LogLevel;
    /**
     * Enable or disable console logging
     */
    setConsoleEnabled(enabled: boolean): void;
    /**
     * Enable or disable file logging
     */
    setFileEnabled(enabled: boolean): void;
    /**
     * Set the log file path
     */
    setLogFile(filePath: string): void;
    /**
     * Get recent log entries
     */
    getRecentLogs(count?: number): LogEntry[];
    /**
     * Clear the log buffer
     */
    clearBuffer(): void;
    /**
     * Export logs to a file
     */
    exportLogs(filePath: string): void;
    /**
     * Create a child logger with a specific source
     */
    child(source: string): Logger;
}
/**
 * Get the global logger instance
 */
export declare function getLogger(): Logger;
/**
 * Set the global logger instance
 */
export declare function setLogger(logger: Logger): void;
/**
 * Create a logger with specific configuration
 */
export declare function createLogger(options: {
    level?: LogLevel;
    logFile?: string;
    enableConsole?: boolean;
    enableFile?: boolean;
    maxBufferSize?: number;
}): Logger;
