{"version": 3, "file": "shell-execute.js", "sourceRoot": "", "sources": ["../../src/tools/shell-execute.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AAC5C,OAAO,EAAE,SAAS,EAAE,MAAM,MAAM,CAAC;AACjC,OAAO,EAAE,CAAC,EAAE,MAAM,KAAK,CAAC;AACxB,OAAO,EAAE,QAAQ,EAA2B,eAAe,EAAE,MAAM,YAAY,CAAC;AAGhF,MAAM,SAAS,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;AAElC,MAAM,kBAAkB,GAAG,CAAC,CAAC,MAAM,CAAC;IAClC,OAAO,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,0BAA0B,CAAC;IACxD,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,yDAAyD,CAAC;IACxG,GAAG,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,mCAAmC,CAAC;IACxE,OAAO,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,yBAAyB,CAAC;IACjF,GAAG,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,uBAAuB,CAAC;IACtE,KAAK,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,sBAAsB,CAAC;IAC5E,aAAa,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,2BAA2B,CAAC;IACzF,WAAW,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,2BAA2B,CAAC;CACzF,CAAC,CAAC;AAEH,MAAM,OAAO,gBAAiB,SAAQ,QAAQ;IAC5C,IAAI,GAAG,eAAe,CAAC;IACvB,WAAW,GAAG,qFAAqF,CAAC;IACpG,UAAU,GAAG,kBAAkB,CAAC;IACvB,QAAQ,GAAG,eAAe,CAAC,KAAK,CAAC;IACjC,gBAAgB,GAAG,IAAI,CAAC,CAAC,2CAA2C;IAEpE,QAAQ,GAAG;QAClB;YACE,WAAW,EAAE,iCAAiC;YAC9C,UAAU,EAAE;gBACV,OAAO,EAAE,QAAQ;aAClB;SACF;QACD;YACE,WAAW,EAAE,8BAA8B;YAC3C,UAAU,EAAE;gBACV,OAAO,EAAE,aAAa;gBACtB,OAAO,EAAE,KAAK;gBACd,GAAG,EAAE,WAAW;aACjB;SACF;QACD;YACE,WAAW,EAAE,iCAAiC;YAC9C,UAAU,EAAE;gBACV,OAAO,EAAE,kBAAkB;gBAC3B,GAAG,EAAE,EAAE,UAAU,EAAE,aAAa,EAAE;aACnC;SACF;KACF,CAAC;IAEF,KAAK,CAAC,OAAO,CAAC,MAAW,EAAE,OAAoB;QAC7C,MAAM,eAAe,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QACpD,MAAM,EACJ,OAAO,EACP,IAAI,EACJ,GAAG,EACH,OAAO,EACP,GAAG,EACH,KAAK,EACL,aAAa,EACb,WAAW,EACZ,GAAG,eAAe,CAAC;QAEpB,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,gBAAgB,CAAC;YACxD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAE7B,gCAAgC;YAChC,MAAM,UAAU,GAAG;gBACjB,GAAG,OAAO,CAAC,GAAG;gBACd,GAAG,GAAG;aACP,CAAC;YAEF,IAAI,MAKH,CAAC;YAEF,IAAI,WAAW,EAAE,CAAC;gBAChB,+DAA+D;gBAC/D,MAAM,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,IAAI,EAAE,UAAU,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;YACzF,CAAC;iBAAM,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACnC,iDAAiD;gBACjD,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,EAAE,UAAU,EAAE,UAAU,EAAE,OAAO,EAAE,aAAa,CAAC,CAAC;YACtG,CAAC;iBAAM,CAAC;gBACN,qCAAqC;gBACrC,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;YACvF,CAAC;YAED,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC3B,MAAM,QAAQ,GAAG,OAAO,GAAG,SAAS,CAAC;YAErC,OAAO,IAAI,CAAC,OAAO,CAAC;gBAClB,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC,GAAG,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO;gBACxD,QAAQ,EAAE,MAAM,CAAC,QAAQ;gBACzB,MAAM,EAAE,MAAM,CAAC,MAAM;gBACrB,MAAM,EAAE,MAAM,CAAC,MAAM;gBACrB,MAAM,EAAE,MAAM,CAAC,MAAM;gBACrB,QAAQ;gBACR,gBAAgB,EAAE,UAAU;gBAC5B,OAAO,EAAE,MAAM,CAAC,QAAQ,KAAK,CAAC;aAC/B,CAAC,CAAC;QAEL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBAC3B,MAAM,IAAI,WAAW,CAAC,2BAA2B,KAAK,CAAC,OAAO,EAAE,EAAE,OAAO,CAAC,CAAC;YAC7E,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,eAAe,CAC3B,OAAe,EACf,GAAW,EACX,GAA2B,EAC3B,OAAe,EACf,KAAc;QAEd,IAAI,CAAC;YACH,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,SAAS,CAAC,OAAO,EAAE;gBAClD,GAAG;gBACH,GAAG;gBACH,OAAO;gBACP,KAAK,EAAE,KAAK,IAAI,KAAK;gBACrB,SAAS,EAAE,IAAI,GAAG,IAAI,EAAE,aAAa;aACtC,CAAC,CAAC;YAEH,OAAO;gBACL,MAAM,EAAE,MAAM,IAAI,EAAE;gBACpB,MAAM,EAAE,MAAM,IAAI,EAAE;gBACpB,QAAQ,EAAE,CAAC;aACZ,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO;gBACL,MAAM,EAAE,KAAK,CAAC,MAAM,IAAI,EAAE;gBAC1B,MAAM,EAAE,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,OAAO,IAAI,EAAE;gBAC3C,QAAQ,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC;aAC1B,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAC5B,OAAe,EACf,IAAc,EACd,GAAW,EACX,GAA2B,EAC3B,OAAe,EACf,aAAsB;QAEtB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,MAAM,KAAK,GAAG,KAAK,CAAC,OAAO,EAAE,IAAI,EAAE;gBACjC,GAAG;gBACH,GAAG;gBACH,KAAK,EAAE,aAAa,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS;aAC1C,CAAC,CAAC;YAEH,IAAI,MAAM,GAAG,EAAE,CAAC;YAChB,IAAI,MAAM,GAAG,EAAE,CAAC;YAChB,IAAI,SAAyB,CAAC;YAE9B,IAAI,aAAa,EAAE,CAAC;gBAClB,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;oBAChC,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAC5B,CAAC,CAAC,CAAC;gBAEH,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;oBAChC,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAC5B,CAAC,CAAC,CAAC;YACL,CAAC;YAED,cAAc;YACd,IAAI,OAAO,GAAG,CAAC,EAAE,CAAC;gBAChB,SAAS,GAAG,UAAU,CAAC,GAAG,EAAE;oBAC1B,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;oBACtB,MAAM,CAAC,IAAI,KAAK,CAAC,2BAA2B,OAAO,IAAI,CAAC,CAAC,CAAC;gBAC5D,CAAC,EAAE,OAAO,CAAC,CAAC;YACd,CAAC;YAED,KAAK,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,IAAI,EAAE,MAAM,EAAE,EAAE;gBACjC,IAAI,SAAS;oBAAE,YAAY,CAAC,SAAS,CAAC,CAAC;gBACvC,OAAO,CAAC;oBACN,MAAM;oBACN,MAAM;oBACN,QAAQ,EAAE,IAAI,IAAI,CAAC;oBACnB,MAAM,EAAE,MAAgB,IAAI,SAAS;iBACtC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,KAAK,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;gBAC1B,IAAI,SAAS;oBAAE,YAAY,CAAC,SAAS,CAAC,CAAC;gBACvC,MAAM,CAAC,KAAK,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAC9B,OAAe,EACf,IAA0B,EAC1B,GAAW,EACX,GAA2B,EAC3B,OAAe;QAEf,sFAAsF;QACtF,MAAM,WAAW,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC;QAEpE,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,MAAM,KAAK,GAAG,KAAK,CAAC,WAAW,EAAE;gBAC/B,GAAG;gBACH,GAAG;gBACH,KAAK,EAAE,SAAS;gBAChB,KAAK,EAAE,IAAI;aACZ,CAAC,CAAC;YAEH,IAAI,SAAyB,CAAC;YAE9B,IAAI,OAAO,GAAG,CAAC,EAAE,CAAC;gBAChB,SAAS,GAAG,UAAU,CAAC,GAAG,EAAE;oBAC1B,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;oBACtB,MAAM,CAAC,IAAI,KAAK,CAAC,uCAAuC,OAAO,IAAI,CAAC,CAAC,CAAC;gBACxE,CAAC,EAAE,OAAO,CAAC,CAAC;YACd,CAAC;YAED,KAAK,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,IAAI,EAAE,EAAE;gBACzB,IAAI,SAAS;oBAAE,YAAY,CAAC,SAAS,CAAC,CAAC;gBACvC,OAAO,CAAC;oBACN,MAAM,EAAE,0CAA0C;oBAClD,MAAM,EAAE,EAAE;oBACV,QAAQ,EAAE,IAAI,IAAI,CAAC;iBACpB,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,KAAK,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;gBAC1B,IAAI,SAAS;oBAAE,YAAY,CAAC,SAAS,CAAC,CAAC;gBACvC,MAAM,CAAC,KAAK,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED,YAAY;QACV,OAAO,MAAM,CAAC,CAAC,sCAAsC;IACvD,CAAC;CACF"}