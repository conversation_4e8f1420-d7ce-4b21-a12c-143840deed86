/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */

import { readFileSync, existsSync, statSync } from 'fs';
import path from 'path';
import { z } from 'zod';
import { BaseTool, ToolContext, ToolResult, TOOL_CATEGORIES } from './tools.js';
import { FileSystemError } from '../utils/errors.js';

const ReadFileParams = z.object({
  path: z.string().describe('Path to the file to read'),
  encoding: z.enum(['utf8', 'base64', 'hex']).optional().default('utf8').describe('File encoding'),
  maxSize: z.number().optional().default(1024 * 1024).describe('Maximum file size in bytes'),
  startLine: z.number().optional().describe('Start reading from this line (1-based)'),
  endLine: z.number().optional().describe('Stop reading at this line (1-based)'),
});

export class ReadFileTool extends BaseTool {
  name = 'read_file';
  description = 'Read the contents of a file. Supports text files with optional line range selection.';
  parameters = ReadFileParams;
  override category = TOOL_CATEGORIES.FILE_SYSTEM;
  override requiresApproval = false;

  override examples = [
    {
      description: 'Read a complete text file',
      parameters: {
        path: './src/config.ts',
      },
    },
    {
      description: 'Read specific lines from a file',
      parameters: {
        path: './README.md',
        startLine: 1,
        endLine: 20,
      },
    },
    {
      description: 'Read a binary file as base64',
      parameters: {
        path: './image.png',
        encoding: 'base64',
      },
    },
  ];

  async execute(params: any, context: ToolContext): Promise<ToolResult> {
    const validatedParams = this.validateParams(params);
    const { path: filePath, encoding, maxSize, startLine, endLine } = validatedParams;

    try {
      // Resolve the file path relative to working directory
      const resolvedPath = path.resolve(context.workingDirectory, filePath);
      
      // Security check: ensure the file is within the working directory
      if (!resolvedPath.startsWith(context.workingDirectory)) {
        return this.error('Access denied: File is outside the working directory');
      }

      // Check if file exists
      if (!existsSync(resolvedPath)) {
        return this.error(`File not found: ${filePath}`);
      }

      // Check if it's a file (not a directory)
      const stats = statSync(resolvedPath);
      if (!stats.isFile()) {
        return this.error(`Path is not a file: ${filePath}`);
      }

      // Check file size
      if (stats.size > maxSize) {
        return this.error(
          `File too large: ${stats.size} bytes (max: ${maxSize} bytes)`,
          { fileSize: stats.size, maxSize }
        );
      }

      // Read the file
      let content: string;
      if (encoding === 'utf8') {
        content = readFileSync(resolvedPath, 'utf8');
        
        // Handle line range if specified
        if (startLine !== undefined || endLine !== undefined) {
          const lines = content.split('\n');
          const start = Math.max(0, (startLine || 1) - 1);
          const end = endLine ? Math.min(lines.length, endLine) : lines.length;
          
          if (start >= lines.length) {
            return this.error(`Start line ${startLine} is beyond file length (${lines.length} lines)`);
          }
          
          content = lines.slice(start, end).join('\n');
        }
      } else {
        const buffer = readFileSync(resolvedPath);
        content = buffer.toString(encoding as BufferEncoding);
      }

      return this.success({
        content,
        path: filePath,
        resolvedPath,
        encoding,
        size: stats.size,
        lines: encoding === 'utf8' ? content.split('\n').length : undefined,
        lastModified: stats.mtime,
      });

    } catch (error) {
      if (error instanceof Error) {
        throw new FileSystemError(`Failed to read file: ${error.message}`, filePath);
      }
      throw error;
    }
  }
}

/**
 * Utility function to check if a file is likely to be text
 */
export function isTextFile(filePath: string): boolean {
  const textExtensions = [
    '.txt', '.md', '.js', '.ts', '.jsx', '.tsx', '.json', '.xml', '.html', '.htm',
    '.css', '.scss', '.sass', '.less', '.py', '.rb', '.php', '.java', '.c', '.cpp',
    '.h', '.hpp', '.cs', '.go', '.rs', '.swift', '.kt', '.scala', '.clj', '.hs',
    '.ml', '.fs', '.vb', '.sql', '.sh', '.bash', '.zsh', '.fish', '.ps1', '.bat',
    '.cmd', '.dockerfile', '.yaml', '.yml', '.toml', '.ini', '.cfg', '.conf',
    '.log', '.csv', '.tsv', '.rtf', '.tex', '.latex', '.bib', '.r', '.R', '.m',
    '.pl', '.pm', '.tcl', '.lua', '.vim', '.emacs', '.gitignore', '.gitattributes',
  ];
  
  const ext = path.extname(filePath).toLowerCase();
  return textExtensions.includes(ext);
}

/**
 * Utility function to get file info without reading content
 */
export function getFileInfo(filePath: string, workingDirectory: string): {
  exists: boolean;
  isFile?: boolean;
  isDirectory?: boolean;
  size?: number;
  lastModified?: Date;
  isText?: boolean;
} {
  try {
    const resolvedPath = path.resolve(workingDirectory, filePath);
    
    if (!existsSync(resolvedPath)) {
      return { exists: false };
    }
    
    const stats = statSync(resolvedPath);
    
    return {
      exists: true,
      isFile: stats.isFile(),
      isDirectory: stats.isDirectory(),
      size: stats.size,
      lastModified: stats.mtime,
      isText: stats.isFile() ? isTextFile(resolvedPath) : undefined,
    };
  } catch {
    return { exists: false };
  }
}
