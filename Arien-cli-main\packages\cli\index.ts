#!/usr/bin/env node

/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

// Suppress specific deprecation warnings from dependencies
process.removeAllListeners('warning');
process.on('warning', (warning) => {
  // Suppress punycode and url.parse deprecation warnings
  if (warning.name === 'DeprecationWarning' && 
      (warning.message.includes('punycode') || 
       warning.message.includes('url.parse'))) {
    return; // Skip these warnings
  }
  // Log other warnings normally
  console.warn(warning.stack);
});

import { main } from './src/arien.js';

// --- Global Entry Point ---
main().catch((error) => {
  console.error('An unexpected critical error occurred:');
  if (error instanceof Error) {
    console.error(error.stack);
  } else {
    console.error(String(error));
  }
  process.exit(1);
});
