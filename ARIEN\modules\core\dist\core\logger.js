/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */
import { writeFileSync, appendFileSync, existsSync, mkdirSync } from 'fs';
import path from 'path';
import os from 'os';
import { sessionId } from '../utils/session.js';
export var LogLevel;
(function (LogLevel) {
    LogLevel[LogLevel["DEBUG"] = 0] = "DEBUG";
    LogLevel[LogLevel["INFO"] = 1] = "INFO";
    LogLevel[LogLevel["WARN"] = 2] = "WARN";
    LogLevel[LogLevel["ERROR"] = 3] = "ERROR";
})(LogLevel || (LogLevel = {}));
export class Logger {
    logLevel = LogLevel.INFO;
    logFile;
    enableConsole = true;
    enableFile = false;
    logBuffer = [];
    maxBufferSize = 1000;
    constructor(options = {}) {
        this.logLevel = options.level ?? LogLevel.INFO;
        this.logFile = options.logFile;
        this.enableConsole = options.enableConsole ?? true;
        this.enableFile = options.enableFile ?? false;
        this.maxBufferSize = options.maxBufferSize ?? 1000;
        if (this.enableFile && this.logFile) {
            this.ensureLogDirectory();
        }
    }
    ensureLogDirectory() {
        if (!this.logFile)
            return;
        const logDir = path.dirname(this.logFile);
        if (!existsSync(logDir)) {
            mkdirSync(logDir, { recursive: true });
        }
    }
    shouldLog(level) {
        return level >= this.logLevel;
    }
    formatMessage(entry) {
        const timestamp = entry.timestamp.toISOString();
        const levelName = LogLevel[entry.level];
        const source = entry.source ? `[${entry.source}]` : '';
        const data = entry.data ? ` ${JSON.stringify(entry.data)}` : '';
        return `${timestamp} ${levelName} ${source} ${entry.message}${data}`;
    }
    writeToConsole(entry) {
        if (!this.enableConsole)
            return;
        const message = this.formatMessage(entry);
        switch (entry.level) {
            case LogLevel.DEBUG:
                console.debug(message);
                break;
            case LogLevel.INFO:
                console.info(message);
                break;
            case LogLevel.WARN:
                console.warn(message);
                break;
            case LogLevel.ERROR:
                console.error(message);
                break;
        }
    }
    writeToFile(entry) {
        if (!this.enableFile || !this.logFile)
            return;
        const message = this.formatMessage(entry) + '\n';
        try {
            appendFileSync(this.logFile, message);
        }
        catch (error) {
            // Fallback to console if file writing fails
            console.error('Failed to write to log file:', error);
            console.log(message.trim());
        }
    }
    addToBuffer(entry) {
        this.logBuffer.push(entry);
        // Keep buffer size under control
        if (this.logBuffer.length > this.maxBufferSize) {
            this.logBuffer.splice(0, this.logBuffer.length - this.maxBufferSize);
        }
    }
    log(level, message, data, source) {
        if (!this.shouldLog(level))
            return;
        const entry = {
            timestamp: new Date(),
            level,
            message,
            data,
            sessionId,
            source,
        };
        this.addToBuffer(entry);
        this.writeToConsole(entry);
        this.writeToFile(entry);
    }
    debug(message, data, source) {
        this.log(LogLevel.DEBUG, message, data, source);
    }
    info(message, data, source) {
        this.log(LogLevel.INFO, message, data, source);
    }
    warn(message, data, source) {
        this.log(LogLevel.WARN, message, data, source);
    }
    error(message, data, source) {
        this.log(LogLevel.ERROR, message, data, source);
    }
    /**
     * Set the log level
     */
    setLevel(level) {
        this.logLevel = level;
    }
    /**
     * Get the current log level
     */
    getLevel() {
        return this.logLevel;
    }
    /**
     * Enable or disable console logging
     */
    setConsoleEnabled(enabled) {
        this.enableConsole = enabled;
    }
    /**
     * Enable or disable file logging
     */
    setFileEnabled(enabled) {
        this.enableFile = enabled;
        if (enabled && this.logFile) {
            this.ensureLogDirectory();
        }
    }
    /**
     * Set the log file path
     */
    setLogFile(filePath) {
        this.logFile = filePath;
        if (this.enableFile) {
            this.ensureLogDirectory();
        }
    }
    /**
     * Get recent log entries
     */
    getRecentLogs(count) {
        const entries = this.logBuffer.slice();
        return count ? entries.slice(-count) : entries;
    }
    /**
     * Clear the log buffer
     */
    clearBuffer() {
        this.logBuffer.length = 0;
    }
    /**
     * Export logs to a file
     */
    exportLogs(filePath) {
        const logs = this.logBuffer.map(entry => this.formatMessage(entry)).join('\n');
        writeFileSync(filePath, logs);
    }
    /**
     * Create a child logger with a specific source
     */
    child(source) {
        const childLogger = new Logger({
            level: this.logLevel,
            logFile: this.logFile,
            enableConsole: this.enableConsole,
            enableFile: this.enableFile,
            maxBufferSize: this.maxBufferSize,
        });
        // Override the log method to include the source
        const originalLog = childLogger.log.bind(childLogger);
        childLogger.log = (level, message, data, childSource) => {
            originalLog(level, message, data, childSource || source);
        };
        return childLogger;
    }
}
// Global logger instance
let globalLogger = null;
/**
 * Get the global logger instance
 */
export function getLogger() {
    if (!globalLogger) {
        const logDir = path.join(os.homedir(), '.arien', 'logs');
        const logFile = path.join(logDir, `arien-${new Date().toISOString().split('T')[0]}.log`);
        globalLogger = new Logger({
            level: LogLevel.INFO,
            logFile,
            enableConsole: true,
            enableFile: true,
        });
    }
    return globalLogger;
}
/**
 * Set the global logger instance
 */
export function setLogger(logger) {
    globalLogger = logger;
}
/**
 * Create a logger with specific configuration
 */
export function createLogger(options) {
    return new Logger(options);
}
//# sourceMappingURL=logger.js.map