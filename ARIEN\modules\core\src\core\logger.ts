/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */

import { writeFileSync, appendFileSync, existsSync, mkdirSync } from 'fs';
import path from 'path';
import os from 'os';
import { sessionId } from '../utils/session.js';

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
}

export interface LogEntry {
  timestamp: Date;
  level: LogLevel;
  message: string;
  data?: any;
  sessionId: string;
  source?: string;
}

export class Logger {
  private logLevel: LogLevel = LogLevel.INFO;
  private logFile?: string;
  private enableConsole = true;
  private enableFile = false;
  private logBuffer: LogEntry[] = [];
  private maxBufferSize = 1000;

  constructor(options: {
    level?: LogLevel;
    logFile?: string;
    enableConsole?: boolean;
    enableFile?: boolean;
    maxBufferSize?: number;
  } = {}) {
    this.logLevel = options.level ?? LogLevel.INFO;
    this.logFile = options.logFile;
    this.enableConsole = options.enableConsole ?? true;
    this.enableFile = options.enableFile ?? false;
    this.maxBufferSize = options.maxBufferSize ?? 1000;

    if (this.enableFile && this.logFile) {
      this.ensureLogDirectory();
    }
  }

  private ensureLogDirectory(): void {
    if (!this.logFile) return;
    
    const logDir = path.dirname(this.logFile);
    if (!existsSync(logDir)) {
      mkdirSync(logDir, { recursive: true });
    }
  }

  private shouldLog(level: LogLevel): boolean {
    return level >= this.logLevel;
  }

  private formatMessage(entry: LogEntry): string {
    const timestamp = entry.timestamp.toISOString();
    const levelName = LogLevel[entry.level];
    const source = entry.source ? `[${entry.source}]` : '';
    const data = entry.data ? ` ${JSON.stringify(entry.data)}` : '';
    
    return `${timestamp} ${levelName} ${source} ${entry.message}${data}`;
  }

  private writeToConsole(entry: LogEntry): void {
    if (!this.enableConsole) return;

    const message = this.formatMessage(entry);
    
    switch (entry.level) {
      case LogLevel.DEBUG:
        console.debug(message);
        break;
      case LogLevel.INFO:
        console.info(message);
        break;
      case LogLevel.WARN:
        console.warn(message);
        break;
      case LogLevel.ERROR:
        console.error(message);
        break;
    }
  }

  private writeToFile(entry: LogEntry): void {
    if (!this.enableFile || !this.logFile) return;

    const message = this.formatMessage(entry) + '\n';
    
    try {
      appendFileSync(this.logFile, message);
    } catch (error) {
      // Fallback to console if file writing fails
      console.error('Failed to write to log file:', error);
      console.log(message.trim());
    }
  }

  private addToBuffer(entry: LogEntry): void {
    this.logBuffer.push(entry);
    
    // Keep buffer size under control
    if (this.logBuffer.length > this.maxBufferSize) {
      this.logBuffer.splice(0, this.logBuffer.length - this.maxBufferSize);
    }
  }

  private log(level: LogLevel, message: string, data?: any, source?: string): void {
    if (!this.shouldLog(level)) return;

    const entry: LogEntry = {
      timestamp: new Date(),
      level,
      message,
      data,
      sessionId,
      source,
    };

    this.addToBuffer(entry);
    this.writeToConsole(entry);
    this.writeToFile(entry);
  }

  debug(message: string, data?: any, source?: string): void {
    this.log(LogLevel.DEBUG, message, data, source);
  }

  info(message: string, data?: any, source?: string): void {
    this.log(LogLevel.INFO, message, data, source);
  }

  warn(message: string, data?: any, source?: string): void {
    this.log(LogLevel.WARN, message, data, source);
  }

  error(message: string, data?: any, source?: string): void {
    this.log(LogLevel.ERROR, message, data, source);
  }

  /**
   * Set the log level
   */
  setLevel(level: LogLevel): void {
    this.logLevel = level;
  }

  /**
   * Get the current log level
   */
  getLevel(): LogLevel {
    return this.logLevel;
  }

  /**
   * Enable or disable console logging
   */
  setConsoleEnabled(enabled: boolean): void {
    this.enableConsole = enabled;
  }

  /**
   * Enable or disable file logging
   */
  setFileEnabled(enabled: boolean): void {
    this.enableFile = enabled;
    if (enabled && this.logFile) {
      this.ensureLogDirectory();
    }
  }

  /**
   * Set the log file path
   */
  setLogFile(filePath: string): void {
    this.logFile = filePath;
    if (this.enableFile) {
      this.ensureLogDirectory();
    }
  }

  /**
   * Get recent log entries
   */
  getRecentLogs(count?: number): LogEntry[] {
    const entries = this.logBuffer.slice();
    return count ? entries.slice(-count) : entries;
  }

  /**
   * Clear the log buffer
   */
  clearBuffer(): void {
    this.logBuffer.length = 0;
  }

  /**
   * Export logs to a file
   */
  exportLogs(filePath: string): void {
    const logs = this.logBuffer.map(entry => this.formatMessage(entry)).join('\n');
    writeFileSync(filePath, logs);
  }

  /**
   * Create a child logger with a specific source
   */
  child(source: string): Logger {
    const childLogger = new Logger({
      level: this.logLevel,
      logFile: this.logFile,
      enableConsole: this.enableConsole,
      enableFile: this.enableFile,
      maxBufferSize: this.maxBufferSize,
    });
    
    // Override the log method to include the source
    const originalLog = childLogger.log.bind(childLogger);
    childLogger.log = (level: LogLevel, message: string, data?: any, childSource?: string) => {
      originalLog(level, message, data, childSource || source);
    };
    
    return childLogger;
  }
}

// Global logger instance
let globalLogger: Logger | null = null;

/**
 * Get the global logger instance
 */
export function getLogger(): Logger {
  if (!globalLogger) {
    const logDir = path.join(os.homedir(), '.arien', 'logs');
    const logFile = path.join(logDir, `arien-${new Date().toISOString().split('T')[0]}.log`);
    
    globalLogger = new Logger({
      level: LogLevel.INFO,
      logFile,
      enableConsole: true,
      enableFile: true,
    });
  }
  return globalLogger;
}

/**
 * Set the global logger instance
 */
export function setLogger(logger: Logger): void {
  globalLogger = logger;
}

/**
 * Create a logger with specific configuration
 */
export function createLogger(options: {
  level?: LogLevel;
  logFile?: string;
  enableConsole?: boolean;
  enableFile?: boolean;
  maxBufferSize?: number;
}): Logger {
  return new Logger(options);
}
