/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */

import { z } from 'zod';

export interface ToolContext {
  workingDirectory: string;
  config: any; // Config type
  sessionId: string;
  userId?: string;
}

export interface ToolResult {
  success: boolean;
  data?: any;
  error?: string;
  metadata?: Record<string, any>;
}

export interface ToolDefinition {
  name: string;
  description: string;
  parameters: z.ZodSchema;
  execute: (params: any, context: ToolContext) => Promise<ToolResult>;
  requiresApproval?: boolean;
  category?: string;
  examples?: Array<{
    description: string;
    parameters: any;
    expectedResult?: any;
  }>;
}

export abstract class BaseTool implements ToolDefinition {
  abstract name: string;
  abstract description: string;
  abstract parameters: z.ZodSchema;
  abstract execute(params: any, context: ToolContext): Promise<ToolResult>;
  
  requiresApproval = false;
  category = 'general';
  examples: Array<{
    description: string;
    parameters: any;
    expectedResult?: any;
  }> = [];

  /**
   * Validate parameters against the schema
   */
  protected validateParams(params: any): any {
    try {
      return this.parameters.parse(params);
    } catch (error) {
      if (error instanceof z.ZodError) {
        const issues = error.issues.map(issue => 
          `${issue.path.join('.')}: ${issue.message}`
        ).join(', ');
        throw new Error(`Parameter validation failed: ${issues}`);
      }
      throw error;
    }
  }

  /**
   * Create a successful result
   */
  protected success(data?: any, metadata?: Record<string, any>): ToolResult {
    return {
      success: true,
      data,
      metadata,
    };
  }

  /**
   * Create an error result
   */
  protected error(message: string, metadata?: Record<string, any>): ToolResult {
    return {
      success: false,
      error: message,
      metadata,
    };
  }

  /**
   * Get the tool definition for AI model registration
   */
  getDefinition(): {
    name: string;
    description: string;
    parameters: {
      type: 'object';
      properties: Record<string, any>;
      required?: string[];
    };
  } {
    return {
      name: this.name,
      description: this.description,
      parameters: this.zodToJsonSchema(this.parameters),
    };
  }

  /**
   * Convert Zod schema to JSON Schema for AI model
   */
  private zodToJsonSchema(schema: z.ZodSchema): {
    type: 'object';
    properties: Record<string, any>;
    required?: string[];
  } {
    // This is a simplified conversion - in a real implementation,
    // you'd want to use a library like zod-to-json-schema
    if (schema instanceof z.ZodObject) {
      const shape = schema.shape;
      const properties: Record<string, any> = {};
      const required: string[] = [];

      for (const [key, value] of Object.entries(shape)) {
        if (value instanceof z.ZodString) {
          properties[key] = { type: 'string' };
          if (!value.isOptional()) required.push(key);
        } else if (value instanceof z.ZodNumber) {
          properties[key] = { type: 'number' };
          if (!value.isOptional()) required.push(key);
        } else if (value instanceof z.ZodBoolean) {
          properties[key] = { type: 'boolean' };
          if (!value.isOptional()) required.push(key);
        } else if (value instanceof z.ZodArray) {
          properties[key] = { type: 'array' };
          if (!value.isOptional()) required.push(key);
        } else {
          properties[key] = { type: 'object' };
          if (!value.isOptional()) required.push(key);
        }
      }

      return {
        type: 'object',
        properties,
        required: required.length > 0 ? required : undefined,
      };
    }

    return {
      type: 'object',
      properties: {},
    };
  }
}

/**
 * Tool execution modes
 */
export enum ToolExecutionMode {
  AUTO = 'auto',           // Execute automatically without approval
  MANUAL = 'manual',       // Require manual approval before execution
  NONE = 'none',          // Don't execute tools, just return the call
}

/**
 * Tool approval request
 */
export interface ToolApprovalRequest {
  toolName: string;
  parameters: any;
  description: string;
  context: ToolContext;
  estimatedRisk: 'low' | 'medium' | 'high';
  preview?: string;
}

/**
 * Tool approval response
 */
export interface ToolApprovalResponse {
  approved: boolean;
  reason?: string;
  modifiedParameters?: any;
}

/**
 * Tool execution statistics
 */
export interface ToolExecutionStats {
  toolName: string;
  executionCount: number;
  successCount: number;
  errorCount: number;
  averageExecutionTime: number;
  lastExecuted?: Date;
}

/**
 * Tool categories for organization
 */
export const TOOL_CATEGORIES = {
  FILE_SYSTEM: 'file-system',
  SHELL: 'shell',
  WEB: 'web',
  MEMORY: 'memory',
  DEVELOPMENT: 'development',
  COMMUNICATION: 'communication',
  UTILITY: 'utility',
} as const;

export type ToolCategory = typeof TOOL_CATEGORIES[keyof typeof TOOL_CATEGORIES];

/**
 * Risk levels for tool execution
 */
export const RISK_LEVELS = {
  LOW: 'low',       // Read-only operations, safe queries
  MEDIUM: 'medium', // File modifications, network requests
  HIGH: 'high',     // System commands, destructive operations
} as const;

export type RiskLevel = typeof RISK_LEVELS[keyof typeof RISK_LEVELS];

/**
 * Utility function to estimate tool risk based on operation type
 */
export function estimateToolRisk(toolName: string, parameters: any): RiskLevel {
  const name = toolName.toLowerCase();
  
  // High risk operations
  if (name.includes('delete') || 
      name.includes('remove') || 
      name.includes('shell') ||
      name.includes('exec') ||
      name.includes('kill')) {
    return RISK_LEVELS.HIGH;
  }
  
  // Medium risk operations
  if (name.includes('write') || 
      name.includes('edit') || 
      name.includes('create') ||
      name.includes('modify') ||
      name.includes('web')) {
    return RISK_LEVELS.MEDIUM;
  }
  
  // Default to low risk for read operations
  return RISK_LEVELS.LOW;
}
