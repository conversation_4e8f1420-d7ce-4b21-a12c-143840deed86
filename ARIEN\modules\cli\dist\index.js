/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */
import { render } from 'ink';
import React from 'react';
import { ArienCLI } from './components/ArienCLI.js';
import { loadCliConfig, initTelemetry, getLogger } from '../../core/dist/index.js';
import { parseArgs } from './utils/args.js';
import { setupErrorHandling } from './utils/error-handling.js';
import { checkSystemRequirements } from './utils/system-check.js';
async function main() {
    try {
        // Setup error handling
        setupErrorHandling();
        // Check system requirements
        await checkSystemRequirements();
        // Parse command line arguments
        const args = parseArgs(process.argv.slice(2));
        // Load configuration
        const config = loadCliConfig(args.config);
        // Initialize telemetry
        const telemetry = initTelemetry(config);
        // Get logger
        const logger = getLogger();
        // Log CLI start
        logger.info('Starting ARIEN AI CLI', {
            version: process.env.CLI_VERSION || '1.0.0',
            args: args,
            nodeVersion: process.version,
        });
        telemetry.logEvent({
            name: 'cli.start',
            properties: {
                version: process.env.CLI_VERSION || '1.0.0',
                args: args,
                nodeVersion: process.version,
            },
        });
        // Handle version flag
        if (args.version) {
            console.log(`ARIEN AI CLI v${process.env.CLI_VERSION || '1.0.0'}`);
            process.exit(0);
        }
        // Handle help flag
        if (args.help) {
            showHelp();
            process.exit(0);
        }
        // Render the CLI application
        const { unmount, waitUntilExit } = render(React.createElement(ArienCLI, {
            config,
            args,
            telemetry,
            logger,
        }));
        // Handle graceful shutdown
        const cleanup = () => {
            logger.info('Shutting down ARIEN AI CLI');
            telemetry.logEvent({
                name: 'cli.exit',
                properties: {
                    reason: 'user_exit',
                },
            });
            unmount();
        };
        process.on('SIGINT', cleanup);
        process.on('SIGTERM', cleanup);
        // Wait for the app to exit
        await waitUntilExit();
    }
    catch (error) {
        const logger = getLogger();
        logger.error('Fatal error in CLI startup', { error });
        console.error('Fatal error:', error instanceof Error ? error.message : String(error));
        process.exit(1);
    }
}
function showHelp() {
    console.log(`
ARIEN AI CLI - Your AI-powered development assistant

Usage:
  arien [options] [command]

Options:
  -h, --help              Show this help message
  -v, --version           Show version information
  -c, --config <path>     Path to configuration file
  -d, --debug             Enable debug mode
  --no-telemetry          Disable telemetry
  --approval-mode <mode>  Set tool approval mode (auto|manual|none)
  --model <model>         Set AI model to use
  --sandbox               Enable sandbox mode
  --no-sandbox            Disable sandbox mode

Commands:
  chat                    Start interactive chat session (default)
  config                  Manage configuration
  tools                   List available tools
  memory                  Manage conversation memory
  auth                    Manage authentication
  version                 Show version information
  help                    Show this help message

Examples:
  arien                           # Start interactive chat
  arien chat                      # Start interactive chat
  arien --model gemini-1.5-pro    # Use specific model
  arien --debug                   # Enable debug logging
  arien config show               # Show current configuration
  arien tools list                # List available tools

For more information, visit: https://github.com/arien-ai/arien-cli
`);
}
// Run the CLI if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    main().catch((error) => {
        console.error('Unhandled error:', error);
        process.exit(1);
    });
}
//# sourceMappingURL=index.js.map