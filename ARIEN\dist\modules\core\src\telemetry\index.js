/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */
export * from './types.js';
export * from './metrics.js';
export * from './loggers.js';
import { TelemetryLogger } from './loggers.js';
import { MetricsCollector } from './metrics.js';
export class TelemetryManager {
    logger;
    metrics;
    enabled;
    constructor(config) {
        this.enabled = config.isTelemetryEnabled();
        this.logger = new TelemetryLogger(this.enabled);
        this.metrics = new MetricsCollector(this.enabled);
    }
    /**
     * Log a telemetry event
     */
    logEvent(event) {
        if (!this.enabled)
            return;
        this.logger.logEvent(event);
    }
    /**
     * Record a metric
     */
    recordMetric(metric) {
        if (!this.enabled)
            return;
        this.metrics.recordMetric(metric);
    }
    /**
     * Start timing an operation
     */
    startTimer(name) {
        if (!this.enabled)
            return () => { };
        return this.metrics.startTimer(name);
    }
    /**
     * Record a counter increment
     */
    incrementCounter(name, value = 1, tags) {
        if (!this.enabled)
            return;
        this.metrics.incrementCounter(name, value, tags);
    }
    /**
     * Record a gauge value
     */
    recordGauge(name, value, tags) {
        if (!this.enabled)
            return;
        this.metrics.recordGauge(name, value, tags);
    }
    /**
     * Get collected metrics
     */
    getMetrics() {
        return this.metrics.getMetrics();
    }
    /**
     * Get logged events
     */
    getEvents() {
        return this.logger.getEvents();
    }
    /**
     * Clear all telemetry data
     */
    clear() {
        this.logger.clear();
        this.metrics.clear();
    }
    /**
     * Enable or disable telemetry
     */
    setEnabled(enabled) {
        this.enabled = enabled;
        this.logger.setEnabled(enabled);
        this.metrics.setEnabled(enabled);
    }
    /**
     * Check if telemetry is enabled
     */
    isEnabled() {
        return this.enabled;
    }
    /**
     * Export telemetry data
     */
    export() {
        return {
            events: this.getEvents(),
            metrics: this.getMetrics(),
            timestamp: new Date(),
        };
    }
}
// Global telemetry manager
let globalTelemetry = null;
/**
 * Get the global telemetry manager
 */
export function getTelemetry() {
    return globalTelemetry;
}
/**
 * Initialize telemetry with configuration
 */
export function initTelemetry(config) {
    globalTelemetry = new TelemetryManager(config);
    return globalTelemetry;
}
/**
 * Convenience functions for common telemetry operations
 */
export function logEvent(event) {
    globalTelemetry?.logEvent(event);
}
export function recordMetric(metric) {
    globalTelemetry?.recordMetric(metric);
}
export function startTimer(name) {
    return globalTelemetry?.startTimer(name) || (() => { });
}
export function incrementCounter(name, value = 1, tags) {
    globalTelemetry?.incrementCounter(name, value, tags);
}
export function recordGauge(name, value, tags) {
    globalTelemetry?.recordGauge(name, value, tags);
}
