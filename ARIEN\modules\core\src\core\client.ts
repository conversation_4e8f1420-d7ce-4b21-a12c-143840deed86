/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */

import { GoogleGenerativeAI, GenerativeModel, GenerationConfig } from '@google/generative-ai';
import { Config } from '../config/config.js';
import { getModelConfig } from '../config/models.js';
import { AuthenticationError, ApiError } from '../utils/errors.js';
import { retryWithBackoff } from '../utils/retry.js';

export interface ChatMessage {
  role: 'user' | 'model';
  parts: Array<{
    text?: string;
    inlineData?: {
      mimeType: string;
      data: string;
    };
    functionCall?: {
      name: string;
      args: Record<string, any>;
    };
    functionResponse?: {
      name: string;
      response: Record<string, any>;
    };
  }>;
}

export interface ChatOptions {
  temperature?: number;
  maxOutputTokens?: number;
  topP?: number;
  topK?: number;
  stopSequences?: string[];
}

export interface ToolDefinition {
  name: string;
  description: string;
  parameters: {
    type: 'object';
    properties: Record<string, any>;
    required?: string[];
  };
}

export class ArienClient {
  private genAI: GoogleGenerativeAI;
  private model: GenerativeModel;
  private config: Config;
  private modelConfig: ReturnType<typeof getModelConfig>;

  constructor(config: Config) {
    this.config = config;
    
    const apiKey = this.getApiKey();
    if (!apiKey) {
      throw new AuthenticationError('No API key provided. Set ARIEN_API_KEY environment variable or configure authentication.');
    }
    
    this.genAI = new GoogleGenerativeAI(apiKey);
    this.modelConfig = getModelConfig(config.getModel());
    
    // Initialize the model with default configuration
    this.model = this.genAI.getGenerativeModel({
      model: this.modelConfig.name,
      generationConfig: this.getGenerationConfig(),
    });
  }

  private getApiKey(): string {
    const authType = this.config.getAuthType();
    
    switch (authType) {
      case 'api-key':
        return this.config.getApiKey() || '';
      case 'service-account':
        // TODO: Implement service account authentication
        throw new AuthenticationError('Service account authentication not yet implemented');
      case 'oauth':
        // TODO: Implement OAuth authentication
        throw new AuthenticationError('OAuth authentication not yet implemented');
      default:
        throw new AuthenticationError(`Unknown authentication type: ${authType}`);
    }
  }

  private getGenerationConfig(): GenerationConfig {
    return {
      temperature: this.config.getTemperature(),
      maxOutputTokens: this.config.getMaxTokens(),
      topP: 0.95,
      topK: 40,
    };
  }

  /**
   * Generate content using the AI model
   */
  async generateContent(
    prompt: string,
    options: ChatOptions = {}
  ): Promise<string> {
    try {
      const generationConfig = {
        ...this.getGenerationConfig(),
        ...options,
      };

      const result = await retryWithBackoff(async () => {
        return this.model.generateContent({
          contents: [{ role: 'user', parts: [{ text: prompt }] }],
          generationConfig,
        });
      });

      const response = result.response;
      const text = response.text();
      
      if (!text) {
        throw new ApiError('Empty response from AI model');
      }
      
      return text;
    } catch (error) {
      if (error instanceof Error) {
        throw new ApiError(`AI generation failed: ${error.message}`);
      }
      throw error;
    }
  }

  /**
   * Start a chat session
   */
  async startChat(
    history: ChatMessage[] = [],
    tools: ToolDefinition[] = []
  ): Promise<ArienChatSession> {
    const modelWithTools = tools.length > 0 
      ? this.genAI.getGenerativeModel({
          model: this.modelConfig.name,
          generationConfig: this.getGenerationConfig(),
          tools: [{ functionDeclarations: tools }],
        })
      : this.model;

    const chat = modelWithTools.startChat({
      history: history.map(msg => ({
        role: msg.role,
        parts: msg.parts,
      })),
    });

    return new ArienChatSession(chat, this.config);
  }

  /**
   * Get model information
   */
  getModelInfo() {
    return {
      name: this.modelConfig.name,
      displayName: this.modelConfig.displayName,
      maxTokens: this.modelConfig.maxTokens,
      supportsTools: this.modelConfig.supportsTools,
      supportsVision: this.modelConfig.supportsVision,
      supportsCodeExecution: this.modelConfig.supportsCodeExecution,
    };
  }

  /**
   * Estimate token count for text
   */
  async countTokens(text: string): Promise<number> {
    try {
      const result = await this.model.countTokens(text);
      return result.totalTokens;
    } catch (error) {
      // Fallback estimation: roughly 4 characters per token
      return Math.ceil(text.length / 4);
    }
  }
}

export class ArienChatSession {
  private chat: any; // GoogleAI ChatSession type
  private config: Config;

  constructor(chat: any, config: Config) {
    this.chat = chat;
    this.config = config;
  }

  /**
   * Send a message in the chat session
   */
  async sendMessage(
    message: string,
    options: ChatOptions = {}
  ): Promise<{
    text: string;
    functionCalls?: Array<{
      name: string;
      args: Record<string, any>;
    }>;
  }> {
    try {
      const result = await retryWithBackoff(async () => {
        return this.chat.sendMessage(message);
      });

      const response = result.response;
      const text = response.text();
      
      // Check for function calls
      const functionCalls = response.functionCalls?.() || [];
      
      return {
        text,
        functionCalls: functionCalls.map((call: any) => ({
          name: call.name,
          args: call.args,
        })),
      };
    } catch (error) {
      if (error instanceof Error) {
        throw new ApiError(`Chat message failed: ${error.message}`);
      }
      throw error;
    }
  }

  /**
   * Send a function response
   */
  async sendFunctionResponse(
    functionName: string,
    response: Record<string, any>
  ): Promise<string> {
    try {
      const result = await retryWithBackoff(async () => {
        return this.chat.sendMessage([
          {
            functionResponse: {
              name: functionName,
              response,
            },
          },
        ]);
      });

      return result.response.text();
    } catch (error) {
      if (error instanceof Error) {
        throw new ApiError(`Function response failed: ${error.message}`);
      }
      throw error;
    }
  }

  /**
   * Get chat history
   */
  getHistory(): ChatMessage[] {
    return this.chat.getHistory().map((msg: any) => ({
      role: msg.role,
      parts: msg.parts,
    }));
  }
}
