{"version": 3, "file": "SyncMetricStorage.js", "sourceRoot": "", "sources": ["../../../src/state/SyncMetricStorage.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAOH,mDAAgD;AAEhD,iEAA8D;AAC9D,uEAAoE;AAIpE;;;;GAIG;AACH,MAAa,iBACX,SAAQ,6BAAa;IAMrB,YACE,oBAA0C,EAC1C,UAAyB,EACjB,oBAAyC,EACjD,gBAAyC;QAEzC,KAAK,CAAC,oBAAoB,CAAC,CAAC;QAHpB,yBAAoB,GAApB,oBAAoB,CAAqB;QAIjD,IAAI,CAAC,mBAAmB,GAAG,IAAI,2CAAoB,CAAC,UAAU,CAAC,CAAC;QAChE,IAAI,CAAC,sBAAsB,GAAG,IAAI,iDAAuB,CACvD,UAAU,EACV,gBAAgB,CACjB,CAAC;IACJ,CAAC;IAED,MAAM,CACJ,KAAa,EACb,UAA4B,EAC5B,OAAgB,EAChB,UAAkB;QAElB,UAAU,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QACpE,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,KAAK,EAAE,UAAU,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;IAC1E,CAAC;IAED;;;;;OAKG;IACH,OAAO,CACL,SAAgC,EAChC,cAAsB;QAEtB,MAAM,aAAa,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,CAAC;QAEzD,OAAO,IAAI,CAAC,sBAAsB,CAAC,YAAY,CAC7C,SAAS,EACT,IAAI,CAAC,qBAAqB,EAC1B,aAAa,EACb,cAAc,CACf,CAAC;IACJ,CAAC;CACF;AAlDD,8CAkDC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Context, HrTime, MetricAttributes } from '@opentelemetry/api';\nimport { WritableMetricStorage } from './WritableMetricStorage';\nimport { Accumulation, Aggregator } from '../aggregator/types';\nimport { InstrumentDescriptor } from '../InstrumentDescriptor';\nimport { AttributesProcessor } from '../view/AttributesProcessor';\nimport { MetricStorage } from './MetricStorage';\nimport { MetricData } from '../export/MetricData';\nimport { DeltaMetricProcessor } from './DeltaMetricProcessor';\nimport { TemporalMetricProcessor } from './TemporalMetricProcessor';\nimport { Maybe } from '../utils';\nimport { MetricCollectorHandle } from './MetricCollector';\n\n/**\n * Internal interface.\n *\n * Stores and aggregates {@link MetricData} for synchronous instruments.\n */\nexport class SyncMetricStorage<T extends Maybe<Accumulation>>\n  extends MetricStorage\n  implements WritableMetricStorage\n{\n  private _deltaMetricStorage: DeltaMetricProcessor<T>;\n  private _temporalMetricStorage: TemporalMetricProcessor<T>;\n\n  constructor(\n    instrumentDescriptor: InstrumentDescriptor,\n    aggregator: Aggregator<T>,\n    private _attributesProcessor: AttributesProcessor,\n    collectorHandles: MetricCollectorHandle[]\n  ) {\n    super(instrumentDescriptor);\n    this._deltaMetricStorage = new DeltaMetricProcessor(aggregator);\n    this._temporalMetricStorage = new TemporalMetricProcessor(\n      aggregator,\n      collectorHandles\n    );\n  }\n\n  record(\n    value: number,\n    attributes: MetricAttributes,\n    context: Context,\n    recordTime: HrTime\n  ) {\n    attributes = this._attributesProcessor.process(attributes, context);\n    this._deltaMetricStorage.record(value, attributes, context, recordTime);\n  }\n\n  /**\n   * Collects the metrics from this storage.\n   *\n   * Note: This is a stateful operation and may reset any interval-related\n   * state for the MetricCollector.\n   */\n  collect(\n    collector: MetricCollectorHandle,\n    collectionTime: HrTime\n  ): Maybe<MetricData> {\n    const accumulations = this._deltaMetricStorage.collect();\n\n    return this._temporalMetricStorage.buildMetrics(\n      collector,\n      this._instrumentDescriptor,\n      accumulations,\n      collectionTime\n    );\n  }\n}\n"]}