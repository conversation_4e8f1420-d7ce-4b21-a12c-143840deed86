/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */
export interface ModelConfig {
    name: string;
    displayName: string;
    maxTokens: number;
    supportsTools: boolean;
    supportsVision: boolean;
    supportsCodeExecution: boolean;
    costPer1kTokens: {
        input: number;
        output: number;
    };
}
export declare const AVAILABLE_MODELS: Record<string, ModelConfig>;
export declare const DEFAULT_ARIEN_FLASH_MODEL = "gemini-1.5-flash";
export declare const DEFAULT_ARIEN_PRO_MODEL = "gemini-1.5-pro";
export declare function getModelConfig(modelName: string): ModelConfig;
export declare function isValidModel(modelName: string): boolean;
export declare function getAvailableModels(): ModelConfig[];
export declare function getDefaultModel(): string;
export declare function getBestModelForTask(task: {
    requiresVision?: boolean;
    requiresCodeExecution?: boolean;
    requiresLargeContext?: boolean;
    prioritizeCost?: boolean;
}): string;
