{"version": 3, "file": "write-file.js", "sourceRoot": "", "sources": ["../../src/tools/write-file.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,aAAa,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,IAAI,CAAC;AACpE,OAAO,IAAI,MAAM,MAAM,CAAC;AACxB,OAAO,EAAE,CAAC,EAAE,MAAM,KAAK,CAAC;AACxB,OAAO,EAAE,QAAQ,EAA2B,eAAe,EAAE,MAAM,YAAY,CAAC;AAChF,OAAO,EAAE,eAAe,EAAE,MAAM,oBAAoB,CAAC;AAErD,MAAM,eAAe,GAAG,CAAC,CAAC,MAAM,CAAC;IAC/B,IAAI,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,2BAA2B,CAAC;IACtD,OAAO,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,8BAA8B,CAAC;IAC5D,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,eAAe,CAAC;IAChG,iBAAiB,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,gDAAgD,CAAC;IAClH,SAAS,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,6BAA6B,CAAC;IACxF,MAAM,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,gCAAgC,CAAC;CACzF,CAAC,CAAC;AAEH,MAAM,OAAO,aAAc,SAAQ,QAAQ;IACzC,IAAI,GAAG,YAAY,CAAC;IACpB,WAAW,GAAG,4EAA4E,CAAC;IAC3F,UAAU,GAAG,eAAe,CAAC;IACpB,QAAQ,GAAG,eAAe,CAAC,WAAW,CAAC;IACvC,gBAAgB,GAAG,IAAI,CAAC,CAAC,kCAAkC;IAE3D,QAAQ,GAAG;QAClB;YACE,WAAW,EAAE,0BAA0B;YACvC,UAAU,EAAE;gBACV,IAAI,EAAE,cAAc;gBACpB,OAAO,EAAE,eAAe;aACzB;SACF;QACD;YACE,WAAW,EAAE,yCAAyC;YACtD,UAAU,EAAE;gBACV,IAAI,EAAE,oBAAoB;gBAC1B,OAAO,EAAE,kBAAkB;gBAC3B,iBAAiB,EAAE,IAAI;aACxB;SACF;QACD;YACE,WAAW,EAAE,qCAAqC;YAClD,UAAU,EAAE;gBACV,IAAI,EAAE,qBAAqB;gBAC3B,OAAO,EAAE,aAAa;gBACtB,SAAS,EAAE,IAAI;gBACf,MAAM,EAAE,IAAI;aACb;SACF;KACF,CAAC;IAEF,KAAK,CAAC,OAAO,CAAC,MAAW,EAAE,OAAoB;QAC7C,MAAM,eAAe,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QACpD,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,iBAAiB,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,eAAe,CAAC;QAEpG,IAAI,CAAC;YACH,sDAAsD;YACtD,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,gBAAgB,EAAE,QAAQ,CAAC,CAAC;YAEtE,kEAAkE;YAClE,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,OAAO,CAAC,gBAAgB,CAAC,EAAE,CAAC;gBACvD,OAAO,IAAI,CAAC,KAAK,CAAC,sDAAsD,CAAC,CAAC;YAC5E,CAAC;YAED,uBAAuB;YACvB,MAAM,UAAU,GAAG,UAAU,CAAC,YAAY,CAAC,CAAC;YAE5C,IAAI,UAAU,IAAI,CAAC,SAAS,EAAE,CAAC;gBAC7B,OAAO,IAAI,CAAC,KAAK,CAAC,wBAAwB,QAAQ,qCAAqC,CAAC,CAAC;YAC3F,CAAC;YAED,6CAA6C;YAC7C,IAAI,UAA8B,CAAC;YACnC,IAAI,MAAM,IAAI,UAAU,EAAE,CAAC;gBACzB,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;gBACjE,UAAU,GAAG,GAAG,YAAY,WAAW,SAAS,EAAE,CAAC;gBAEnD,IAAI,CAAC;oBACH,MAAM,eAAe,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;oBACjE,aAAa,CAAC,UAAU,EAAE,eAAe,CAAC,CAAC;gBAC7C,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,OAAO,IAAI,CAAC,KAAK,CAAC,4BAA4B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;gBAC1G,CAAC;YACH,CAAC;YAED,sCAAsC;YACtC,IAAI,iBAAiB,EAAE,CAAC;gBACtB,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;gBAC7C,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;oBAC3B,SAAS,CAAC,SAAS,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;gBAC5C,CAAC;YACH,CAAC;YAED,iBAAiB;YACjB,IAAI,YAAoB,CAAC;YACzB,IAAI,QAAQ,KAAK,MAAM,EAAE,CAAC;gBACxB,aAAa,CAAC,YAAY,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;gBAC7C,YAAY,GAAG,MAAM,CAAC,UAAU,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;YACpD,CAAC;iBAAM,CAAC;gBACN,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,QAA0B,CAAC,CAAC;gBAChE,aAAa,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;gBACpC,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC;YAC/B,CAAC;YAED,iBAAiB;YACjB,MAAM,KAAK,GAAG,QAAQ,CAAC,YAAY,CAAC,CAAC;YAErC,OAAO,IAAI,CAAC,OAAO,CAAC;gBAClB,IAAI,EAAE,QAAQ;gBACd,YAAY;gBACZ,YAAY;gBACZ,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,OAAO,EAAE,CAAC,UAAU;gBACpB,WAAW,EAAE,UAAU,IAAI,SAAS;gBACpC,aAAa,EAAE,CAAC,CAAC,UAAU;gBAC3B,UAAU;gBACV,QAAQ;gBACR,YAAY,EAAE,KAAK,CAAC,KAAK;aAC1B,CAAC,CAAC;QAEL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBAC3B,MAAM,IAAI,eAAe,CAAC,yBAAyB,KAAK,CAAC,OAAO,EAAE,EAAE,QAAQ,CAAC,CAAC;YAChF,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;CACF;AAED;;GAEG;AACH,MAAM,UAAU,aAAa,CAAC,QAAgB,EAAE,IAAS,EAAE,gBAAwB;IACjF,MAAM,IAAI,GAAG,IAAI,aAAa,EAAE,CAAC;IACjC,OAAO,IAAI,CAAC,OAAO,CAAC;QAClB,IAAI,EAAE,QAAQ;QACd,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;QACtC,iBAAiB,EAAE,IAAI;KACxB,EAAE;QACD,gBAAgB;QAChB,MAAM,EAAE,EAAE;QACV,SAAS,EAAE,cAAc;KAC1B,CAAC,CAAC;AACL,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,YAAY,CAAC,QAAgB,EAAE,OAAe,EAAE,gBAAwB;IACtF,MAAM,EAAE,YAAY,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IACvC,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,QAAQ,CAAC,CAAC;IAE9D,IAAI,eAAe,GAAG,EAAE,CAAC;IACzB,IAAI,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC;QAC7B,IAAI,CAAC;YACH,eAAe,GAAG,YAAY,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;QACvD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,8DAA8D;QAChE,CAAC;IACH,CAAC;IAED,MAAM,IAAI,GAAG,IAAI,aAAa,EAAE,CAAC;IACjC,OAAO,IAAI,CAAC,OAAO,CAAC;QAClB,IAAI,EAAE,QAAQ;QACd,OAAO,EAAE,eAAe,GAAG,OAAO;QAClC,SAAS,EAAE,IAAI;QACf,MAAM,EAAE,eAAe,CAAC,MAAM,GAAG,CAAC;KACnC,EAAE;QACD,gBAAgB;QAChB,MAAM,EAAE,EAAE;QACV,SAAS,EAAE,cAAc;KAC1B,CAAC,CAAC;AACL,CAAC"}