/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import React from 'react';
import { Text, Box } from 'ink';
import { Colors } from '../../colors.js';

interface ErrorMessageProps {
  text: string;
}

export const ErrorMessage: React.FC<ErrorMessageProps> = ({ text }) => {
  const prefix = '✗ ';
  const prefixWidth = prefix.length;

  // Extract error type if present (e.g., "TypeError:", "SyntaxError:")
  const errorMatch = text.match(/^(\w+Error):\s*(.+)$/);
  const errorType = errorMatch ? errorMatch[1] : null;
  const errorDetail = errorMatch ? errorMatch[2] : text;

  return (
    <Box flexDirection="column" marginBottom={1}>
      <Box flexDirection="row">
        <Box width={prefixWidth}>
          <Text color={Colors.AccentRed} bold>{prefix}</Text>
        </Box>
        <Box flexGrow={1} flexDirection="column">
          {errorType && (
            <Text color={Colors.AccentRed} bold>
              {errorType}
            </Text>
          )}
          <Text wrap="wrap" color={Colors.AccentRed}>
            {errorDetail}
          </Text>
        </Box>
      </Box>
      {/* Add subtle separator for multiple errors */}
      <Box marginTop={0} paddingLeft={prefixWidth}>
        <Text color={Colors.Gray} dimColor>
          {'─'.repeat(Math.min(errorDetail.length, 50))}
        </Text>
      </Box>
    </Box>
  );
};
