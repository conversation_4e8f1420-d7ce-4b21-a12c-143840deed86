/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */

import { sessionId } from '../utils/session.js';
import { TelemetryMetric, CounterMetric, GaugeMetric, TimerMetric } from './types.js';

export class MetricsCollector {
  private metrics: TelemetryMetric[] = [];
  private counters = new Map<string, number>();
  private gauges = new Map<string, number>();
  private timers = new Map<string, { startTime: Date; name: string }>();
  private enabled: boolean;
  private maxMetrics = 10000;

  constructor(enabled = true) {
    this.enabled = enabled;
  }

  /**
   * Record a metric
   */
  recordMetric(metric: TelemetryMetric): void {
    if (!this.enabled) return;

    this.metrics.push(metric);
    
    // Keep metrics under control
    if (this.metrics.length > this.maxMetrics) {
      this.metrics.splice(0, this.metrics.length - this.maxMetrics);
    }
  }

  /**
   * Increment a counter
   */
  incrementCounter(name: string, value = 1, tags?: Record<string, string>): void {
    if (!this.enabled) return;

    const key = this.getMetricKey(name, tags);
    const currentValue = this.counters.get(key) || 0;
    const newValue = currentValue + value;
    this.counters.set(key, newValue);

    const metric: CounterMetric = {
      name,
      type: 'counter',
      value: newValue,
      increment: value,
      timestamp: new Date(),
      sessionId,
      tags,
    };

    this.recordMetric(metric);
  }

  /**
   * Record a gauge value
   */
  recordGauge(name: string, value: number, tags?: Record<string, string>): void {
    if (!this.enabled) return;

    const key = this.getMetricKey(name, tags);
    this.gauges.set(key, value);

    const metric: GaugeMetric = {
      name,
      type: 'gauge',
      value,
      timestamp: new Date(),
      sessionId,
      tags,
    };

    this.recordMetric(metric);
  }

  /**
   * Start a timer
   */
  startTimer(name: string, tags?: Record<string, string>): () => void {
    if (!this.enabled) return () => {};

    const key = this.getMetricKey(name, tags);
    const startTime = new Date();
    
    this.timers.set(key, { startTime, name });

    return () => {
      const timer = this.timers.get(key);
      if (!timer) return;

      const endTime = new Date();
      const duration = endTime.getTime() - timer.startTime.getTime();

      const metric: TimerMetric = {
        name,
        type: 'timer',
        value: duration,
        duration,
        startTime: timer.startTime,
        endTime,
        timestamp: endTime,
        sessionId,
        tags,
        unit: 'ms',
      };

      this.recordMetric(metric);
      this.timers.delete(key);
    };
  }

  /**
   * Record a histogram value
   */
  recordHistogram(name: string, value: number, buckets: number[], tags?: Record<string, string>): void {
    if (!this.enabled) return;

    // Find the appropriate bucket
    const bucketCounts: Record<string, number> = {};
    for (const bucket of buckets) {
      const bucketKey = bucket.toString();
      bucketCounts[bucketKey] = value <= bucket ? 1 : 0;
    }

    const metric: TelemetryMetric = {
      name,
      type: 'histogram',
      value,
      timestamp: new Date(),
      sessionId,
      tags,
    };

    this.recordMetric(metric);
  }

  /**
   * Get all recorded metrics
   */
  getMetrics(): TelemetryMetric[] {
    return [...this.metrics];
  }

  /**
   * Get metrics by type
   */
  getMetricsByType(type: TelemetryMetric['type']): TelemetryMetric[] {
    return this.metrics.filter(metric => metric.type === type);
  }

  /**
   * Get metrics by name
   */
  getMetricsByName(name: string): TelemetryMetric[] {
    return this.metrics.filter(metric => metric.name === name);
  }

  /**
   * Get current counter values
   */
  getCounters(): Map<string, number> {
    return new Map(this.counters);
  }

  /**
   * Get current gauge values
   */
  getGauges(): Map<string, number> {
    return new Map(this.gauges);
  }

  /**
   * Get active timers
   */
  getActiveTimers(): string[] {
    return Array.from(this.timers.keys());
  }

  /**
   * Clear all metrics
   */
  clear(): void {
    this.metrics.length = 0;
    this.counters.clear();
    this.gauges.clear();
    this.timers.clear();
  }

  /**
   * Enable or disable metrics collection
   */
  setEnabled(enabled: boolean): void {
    this.enabled = enabled;
  }

  /**
   * Check if metrics collection is enabled
   */
  isEnabled(): boolean {
    return this.enabled;
  }

  /**
   * Set the maximum number of metrics to keep
   */
  setMaxMetrics(max: number): void {
    this.maxMetrics = max;
    
    // Trim existing metrics if necessary
    if (this.metrics.length > max) {
      this.metrics.splice(0, this.metrics.length - max);
    }
  }

  /**
   * Get metrics summary
   */
  getSummary(): {
    totalMetrics: number;
    counters: number;
    gauges: number;
    timers: number;
    histograms: number;
    activeTimers: number;
  } {
    const metricsByType = this.metrics.reduce((acc, metric) => {
      acc[metric.type] = (acc[metric.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      totalMetrics: this.metrics.length,
      counters: metricsByType.counter || 0,
      gauges: metricsByType.gauge || 0,
      timers: metricsByType.timer || 0,
      histograms: metricsByType.histogram || 0,
      activeTimers: this.timers.size,
    };
  }

  /**
   * Generate a unique key for a metric with tags
   */
  private getMetricKey(name: string, tags?: Record<string, string>): string {
    if (!tags || Object.keys(tags).length === 0) {
      return name;
    }

    const sortedTags = Object.entries(tags)
      .sort(([a], [b]) => a.localeCompare(b))
      .map(([key, value]) => `${key}=${value}`)
      .join(',');

    return `${name}{${sortedTags}}`;
  }
}
