/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */
// Common event names
export const TELEMETRY_EVENTS = {
    // CLI Events
    CLI_START: 'cli.start',
    CLI_EXIT: 'cli.exit',
    CLI_COMMAND: 'cli.command',
    CLI_ERROR: 'cli.error',
    // Chat Events
    CHAT_START: 'chat.start',
    CHAT_MESSAGE: 'chat.message',
    CHAT_RESPONSE: 'chat.response',
    CHAT_END: 'chat.end',
    // Tool Events
    TOOL_EXECUTE: 'tool.execute',
    TOOL_SUCCESS: 'tool.success',
    TOOL_ERROR: 'tool.error',
    TOOL_APPROVAL_REQUEST: 'tool.approval.request',
    TOOL_APPROVAL_GRANTED: 'tool.approval.granted',
    TOOL_APPROVAL_DENIED: 'tool.approval.denied',
    // AI Events
    AI_REQUEST: 'ai.request',
    AI_RESPONSE: 'ai.response',
    AI_ERROR: 'ai.error',
    AI_TOKEN_USAGE: 'ai.token.usage',
    // File Events
    FILE_READ: 'file.read',
    FILE_WRITE: 'file.write',
    FILE_DELETE: 'file.delete',
    FILE_ERROR: 'file.error',
    // Authentication Events
    AUTH_LOGIN: 'auth.login',
    AUTH_LOGOUT: 'auth.logout',
    AUTH_ERROR: 'auth.error',
    // Configuration Events
    CONFIG_LOAD: 'config.load',
    CONFIG_UPDATE: 'config.update',
    CONFIG_ERROR: 'config.error',
    // Memory Events
    MEMORY_SAVE: 'memory.save',
    MEMORY_LOAD: 'memory.load',
    MEMORY_DELETE: 'memory.delete',
    MEMORY_ERROR: 'memory.error',
    // Extension Events
    EXTENSION_LOAD: 'extension.load',
    EXTENSION_UNLOAD: 'extension.unload',
    EXTENSION_ERROR: 'extension.error',
};
// Common metric names
export const TELEMETRY_METRICS = {
    // Performance Metrics
    RESPONSE_TIME: 'response.time',
    TOKEN_COUNT: 'token.count',
    REQUEST_COUNT: 'request.count',
    ERROR_COUNT: 'error.count',
    // Usage Metrics
    TOOL_USAGE: 'tool.usage',
    COMMAND_USAGE: 'command.usage',
    MODEL_USAGE: 'model.usage',
    // System Metrics
    MEMORY_USAGE: 'memory.usage',
    CPU_USAGE: 'cpu.usage',
    DISK_USAGE: 'disk.usage',
    // Session Metrics
    SESSION_DURATION: 'session.duration',
    MESSAGES_PER_SESSION: 'session.messages',
    TOOLS_PER_SESSION: 'session.tools',
};
