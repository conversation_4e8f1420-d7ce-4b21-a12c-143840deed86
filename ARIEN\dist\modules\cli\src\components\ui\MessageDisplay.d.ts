/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */
import React from 'react';
import { Theme } from './ThemeProvider.js';
export interface ChatMessage {
    id: string;
    role: 'user' | 'assistant';
    content: string;
    timestamp: Date;
    tokens?: number;
}
export interface MessageDisplayProps {
    message: ChatMessage;
    theme: Theme;
    showTimestamp?: boolean;
    showTokens?: boolean;
}
export declare const MessageDisplay: React.FC<MessageDisplayProps>;
