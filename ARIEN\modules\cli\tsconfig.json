{"extends": "../../tsconfig.json", "compilerOptions": {"outDir": "./dist", "rootDir": "./src", "declaration": true, "declarationMap": true, "sourceMap": true, "composite": true, "incremental": true, "jsx": "react-jsx"}, "include": ["src/**/*", "index.ts"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.test.tsx", "**/__tests__/**/*", "**/__mocks__/**/*"], "references": [{"path": "../core"}]}