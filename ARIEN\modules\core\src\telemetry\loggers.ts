/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */

import { sessionId } from '../utils/session.js';
import { TelemetryEvent } from './types.js';

export class TelemetryLogger {
  private events: TelemetryEvent[] = [];
  private enabled: boolean;
  private maxEvents = 10000;

  constructor(enabled = true) {
    this.enabled = enabled;
  }

  /**
   * Log a telemetry event
   */
  logEvent(event: Omit<TelemetryEvent, 'timestamp' | 'sessionId'>): void {
    if (!this.enabled) return;

    const fullEvent: TelemetryEvent = {
      ...event,
      timestamp: new Date(),
      sessionId,
    };

    this.events.push(fullEvent);

    // Keep events under control
    if (this.events.length > this.maxEvents) {
      this.events.splice(0, this.events.length - this.maxEvents);
    }
  }

  /**
   * Get all logged events
   */
  getEvents(): TelemetryEvent[] {
    return [...this.events];
  }

  /**
   * Get events by name
   */
  getEventsByName(name: string): TelemetryEvent[] {
    return this.events.filter(event => event.name === name);
  }

  /**
   * Get events within a time range
   */
  getEventsByTimeRange(startTime: Date, endTime: Date): TelemetryEvent[] {
    return this.events.filter(
      event => event.timestamp >= startTime && event.timestamp <= endTime
    );
  }

  /**
   * Get events by session ID
   */
  getEventsBySession(sessionId: string): TelemetryEvent[] {
    return this.events.filter(event => event.sessionId === sessionId);
  }

  /**
   * Get events by user ID
   */
  getEventsByUser(userId: string): TelemetryEvent[] {
    return this.events.filter(event => event.userId === userId);
  }

  /**
   * Get events with specific tags
   */
  getEventsByTags(tags: Record<string, string>): TelemetryEvent[] {
    return this.events.filter(event => {
      if (!event.tags) return false;
      
      return Object.entries(tags).every(([key, value]) => 
        event.tags![key] === value
      );
    });
  }

  /**
   * Clear all events
   */
  clear(): void {
    this.events.length = 0;
  }

  /**
   * Enable or disable event logging
   */
  setEnabled(enabled: boolean): void {
    this.enabled = enabled;
  }

  /**
   * Check if event logging is enabled
   */
  isEnabled(): boolean {
    return this.enabled;
  }

  /**
   * Set the maximum number of events to keep
   */
  setMaxEvents(max: number): void {
    this.maxEvents = max;
    
    // Trim existing events if necessary
    if (this.events.length > max) {
      this.events.splice(0, this.events.length - max);
    }
  }

  /**
   * Get event statistics
   */
  getEventStats(): {
    totalEvents: number;
    eventsByName: Record<string, number>;
    eventsBySession: Record<string, number>;
    eventsByUser: Record<string, number>;
    timeRange: { start?: Date; end?: Date };
  } {
    const eventsByName: Record<string, number> = {};
    const eventsBySession: Record<string, number> = {};
    const eventsByUser: Record<string, number> = {};
    let startTime: Date | undefined;
    let endTime: Date | undefined;

    for (const event of this.events) {
      // Count by name
      eventsByName[event.name] = (eventsByName[event.name] || 0) + 1;
      
      // Count by session
      eventsBySession[event.sessionId] = (eventsBySession[event.sessionId] || 0) + 1;
      
      // Count by user
      if (event.userId) {
        eventsByUser[event.userId] = (eventsByUser[event.userId] || 0) + 1;
      }
      
      // Track time range
      if (!startTime || event.timestamp < startTime) {
        startTime = event.timestamp;
      }
      if (!endTime || event.timestamp > endTime) {
        endTime = event.timestamp;
      }
    }

    return {
      totalEvents: this.events.length,
      eventsByName,
      eventsBySession,
      eventsByUser,
      timeRange: { start: startTime, end: endTime },
    };
  }

  /**
   * Export events to a structured format
   */
  exportEvents(): {
    events: TelemetryEvent[];
    metadata: {
      exportTime: Date;
      totalEvents: number;
      sessionId: string;
    };
  } {
    return {
      events: this.getEvents(),
      metadata: {
        exportTime: new Date(),
        totalEvents: this.events.length,
        sessionId,
      },
    };
  }

  /**
   * Import events from a structured format
   */
  importEvents(data: { events: TelemetryEvent[] }): void {
    if (!Array.isArray(data.events)) {
      throw new Error('Invalid events data format');
    }

    for (const event of data.events) {
      if (!event.name || !event.timestamp || !event.sessionId) {
        throw new Error('Invalid event format');
      }
    }

    this.events.push(...data.events);

    // Trim if necessary
    if (this.events.length > this.maxEvents) {
      this.events.splice(0, this.events.length - this.maxEvents);
    }
  }

  /**
   * Get recent events
   */
  getRecentEvents(count: number): TelemetryEvent[] {
    return this.events.slice(-count);
  }

  /**
   * Search events by property
   */
  searchEvents(query: {
    name?: string;
    userId?: string;
    sessionId?: string;
    properties?: Record<string, any>;
    tags?: Record<string, string>;
    startTime?: Date;
    endTime?: Date;
  }): TelemetryEvent[] {
    return this.events.filter(event => {
      // Filter by name
      if (query.name && event.name !== query.name) {
        return false;
      }

      // Filter by user ID
      if (query.userId && event.userId !== query.userId) {
        return false;
      }

      // Filter by session ID
      if (query.sessionId && event.sessionId !== query.sessionId) {
        return false;
      }

      // Filter by time range
      if (query.startTime && event.timestamp < query.startTime) {
        return false;
      }
      if (query.endTime && event.timestamp > query.endTime) {
        return false;
      }

      // Filter by properties
      if (query.properties) {
        for (const [key, value] of Object.entries(query.properties)) {
          if (!event.properties || event.properties[key] !== value) {
            return false;
          }
        }
      }

      // Filter by tags
      if (query.tags) {
        for (const [key, value] of Object.entries(query.tags)) {
          if (!event.tags || event.tags[key] !== value) {
            return false;
          }
        }
      }

      return true;
    });
  }
}
