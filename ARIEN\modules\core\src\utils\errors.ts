/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */

export class ArienError extends Error {
  public readonly code: string;
  public readonly details?: Record<string, unknown>;

  constructor(message: string, code: string, details?: Record<string, unknown>) {
    super(message);
    this.name = 'ArienError';
    this.code = code;
    this.details = details;
  }
}

export class ConfigurationError extends ArienError {
  constructor(message: string, details?: Record<string, unknown>) {
    super(message, 'CONFIGURATION_ERROR', details);
    this.name = 'ConfigurationError';
  }
}

export class AuthenticationError extends ArienError {
  constructor(message: string, details?: Record<string, unknown>) {
    super(message, 'AUTHENTICATION_ERROR', details);
    this.name = 'AuthenticationError';
  }
}

export class ApiError extends ArienError {
  public readonly statusCode?: number;

  constructor(
    message: string,
    statusCode?: number,
    details?: Record<string, unknown>
  ) {
    super(message, 'API_ERROR', details);
    this.name = 'ApiError';
    this.statusCode = statusCode;
  }
}

export class ToolExecutionError extends ArienError {
  public readonly toolName: string;

  constructor(
    message: string,
    toolName: string,
    details?: Record<string, unknown>
  ) {
    super(message, 'TOOL_EXECUTION_ERROR', details);
    this.name = 'ToolExecutionError';
    this.toolName = toolName;
  }
}

export class ValidationError extends ArienError {
  public readonly field?: string;

  constructor(
    message: string,
    field?: string,
    details?: Record<string, unknown>
  ) {
    super(message, 'VALIDATION_ERROR', details);
    this.name = 'ValidationError';
    this.field = field;
  }
}

export class FileSystemError extends ArienError {
  public readonly path?: string;

  constructor(
    message: string,
    path?: string,
    details?: Record<string, unknown>
  ) {
    super(message, 'FILESYSTEM_ERROR', details);
    this.name = 'FileSystemError';
    this.path = path;
  }
}

export class NetworkError extends ArienError {
  public readonly url?: string;

  constructor(
    message: string,
    url?: string,
    details?: Record<string, unknown>
  ) {
    super(message, 'NETWORK_ERROR', details);
    this.name = 'NetworkError';
    this.url = url;
  }
}

export class SandboxError extends ArienError {
  constructor(message: string, details?: Record<string, unknown>) {
    super(message, 'SANDBOX_ERROR', details);
    this.name = 'SandboxError';
  }
}

export class TimeoutError extends ArienError {
  public readonly timeoutMs: number;

  constructor(message: string, timeoutMs: number) {
    super(message, 'TIMEOUT_ERROR', { timeoutMs });
    this.name = 'TimeoutError';
    this.timeoutMs = timeoutMs;
  }
}

/**
 * Utility function to check if an error is of a specific type
 */
export function isArienError(error: unknown): error is ArienError {
  return error instanceof ArienError;
}

/**
 * Utility function to extract error message from any error type
 */
export function getErrorMessage(error: unknown): string {
  if (error instanceof Error) {
    return error.message;
  }
  if (typeof error === 'string') {
    return error;
  }
  return 'Unknown error occurred';
}

/**
 * Utility function to extract error details for logging
 */
export function getErrorDetails(error: unknown): Record<string, unknown> {
  if (isArienError(error)) {
    return {
      name: error.name,
      code: error.code,
      message: error.message,
      details: error.details,
      stack: error.stack,
    };
  }
  
  if (error instanceof Error) {
    return {
      name: error.name,
      message: error.message,
      stack: error.stack,
    };
  }
  
  return {
    error: String(error),
  };
}

/**
 * Utility function to create a standardized error response
 */
export function createErrorResponse(error: unknown): {
  success: false;
  error: {
    message: string;
    code?: string;
    details?: Record<string, unknown>;
  };
} {
  const message = getErrorMessage(error);
  const code = isArienError(error) ? error.code : undefined;
  const details = isArienError(error) ? error.details : undefined;

  return {
    success: false,
    error: {
      message,
      code,
      details,
    },
  };
}
