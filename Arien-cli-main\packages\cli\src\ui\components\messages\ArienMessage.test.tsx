/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { describe, it, expect, vi } from 'vitest';
import { render } from '@testing-library/react';
import { ArienMessage } from './ArienMessage.js';

// Mock the dependencies
vi.mock('../../utils/MarkdownDisplay.js', () => ({
  MarkdownDisplay: ({ text }: { text: string }) => <div data-testid="markdown-display">{text}</div>,
}));

vi.mock('../shared/AnimatedIcon.js', () => ({
  AnimatedIcon: ({ isPending, color }: { isPending: boolean; color: string }) => (
    <div data-testid="animated-icon" data-pending={isPending} data-color={color}>
      {isPending ? '⏳' : '🤖'}
    </div>
  ),
}));

vi.mock('../../colors.js', () => ({
  Colors: {
    AccentPurple: '#9333ea',
  },
}));

describe('ArienMessage', () => {
  const defaultProps = {
    text: 'Hello, world!',
    isPending: false,
    terminalWidth: 80,
  };

  it('should render without crashing', () => {
    const { container } = render(<ArienMessage {...defaultProps} />);
    expect(container).toBeDefined();
  });

  it('should display message text in MarkdownDisplay', () => {
    const messageText = 'Test message content';
    const { getByTestId } = render(
      <ArienMessage {...defaultProps} text={messageText} />
    );
    
    const markdownDisplay = getByTestId('markdown-display');
    expect(markdownDisplay).toBeDefined();
    expect(markdownDisplay.textContent).toBe(messageText);
  });

  it('should show animated icon with pending state', () => {
    const { getByTestId } = render(
      <ArienMessage {...defaultProps} isPending={true} />
    );
    
    const animatedIcon = getByTestId('animated-icon');
    expect(animatedIcon).toBeDefined();
    expect(animatedIcon.getAttribute('data-pending')).toBe('true');
    expect(animatedIcon.textContent).toBe('⏳');
  });

  it('should show animated icon with non-pending state', () => {
    const { getByTestId } = render(
      <ArienMessage {...defaultProps} isPending={false} />
    );
    
    const animatedIcon = getByTestId('animated-icon');
    expect(animatedIcon).toBeDefined();
    expect(animatedIcon.getAttribute('data-pending')).toBe('false');
    expect(animatedIcon.textContent).toBe('');
  });

  it('should pass availableTerminalHeight to MarkdownDisplay when provided', () => {
    const { getByTestId } = render(
      <ArienMessage {...defaultProps} availableTerminalHeight={50} />
    );
    
    const markdownDisplay = getByTestId('markdown-display');
    expect(markdownDisplay).toBeDefined();
  });

  it('should pass terminalWidth to MarkdownDisplay', () => {
    const terminalWidth = 120;
    const { getByTestId } = render(
      <ArienMessage {...defaultProps} terminalWidth={terminalWidth} />
    );
    
    const markdownDisplay = getByTestId('markdown-display');
    expect(markdownDisplay).toBeDefined();
  });
}); 