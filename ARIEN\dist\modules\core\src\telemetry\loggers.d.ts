/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */
import { TelemetryEvent } from './types.js';
export declare class TelemetryLogger {
    private events;
    private enabled;
    private maxEvents;
    constructor(enabled?: boolean);
    /**
     * Log a telemetry event
     */
    logEvent(event: Omit<TelemetryEvent, 'timestamp' | 'sessionId'>): void;
    /**
     * Get all logged events
     */
    getEvents(): TelemetryEvent[];
    /**
     * Get events by name
     */
    getEventsByName(name: string): TelemetryEvent[];
    /**
     * Get events within a time range
     */
    getEventsByTimeRange(startTime: Date, endTime: Date): TelemetryEvent[];
    /**
     * Get events by session ID
     */
    getEventsBySession(sessionId: string): TelemetryEvent[];
    /**
     * Get events by user ID
     */
    getEventsByUser(userId: string): TelemetryEvent[];
    /**
     * Get events with specific tags
     */
    getEventsByTags(tags: Record<string, string>): TelemetryEvent[];
    /**
     * Clear all events
     */
    clear(): void;
    /**
     * Enable or disable event logging
     */
    setEnabled(enabled: boolean): void;
    /**
     * Check if event logging is enabled
     */
    isEnabled(): boolean;
    /**
     * Set the maximum number of events to keep
     */
    setMaxEvents(max: number): void;
    /**
     * Get event statistics
     */
    getEventStats(): {
        totalEvents: number;
        eventsByName: Record<string, number>;
        eventsBySession: Record<string, number>;
        eventsByUser: Record<string, number>;
        timeRange: {
            start?: Date;
            end?: Date;
        };
    };
    /**
     * Export events to a structured format
     */
    exportEvents(): {
        events: TelemetryEvent[];
        metadata: {
            exportTime: Date;
            totalEvents: number;
            sessionId: string;
        };
    };
    /**
     * Import events from a structured format
     */
    importEvents(data: {
        events: TelemetryEvent[];
    }): void;
    /**
     * Get recent events
     */
    getRecentEvents(count: number): TelemetryEvent[];
    /**
     * Search events by property
     */
    searchEvents(query: {
        name?: string;
        userId?: string;
        sessionId?: string;
        properties?: Record<string, any>;
        tags?: Record<string, string>;
        startTime?: Date;
        endTime?: Date;
    }): TelemetryEvent[];
}
