/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */

import { readFileSync, existsSync } from 'fs';
import path from 'path';
import os from 'os';
import { stripJsonComments } from '../utils/json-utils.js';

export type AuthType = 'oauth' | 'api-key' | 'service-account';

export type ApprovalMode = 'auto' | 'manual' | 'none';

export interface EditTool {
  name: string;
  command: string;
  args?: string[];
}

export interface ShellTool {
  name: string;
  command: string;
  args?: string[];
}

export interface WriteFileTool {
  enabled: boolean;
  maxFileSize?: number;
}

export interface ConfigData {
  // Authentication
  authType?: AuthType;
  apiKey?: string;
  serviceAccountPath?: string;
  
  // Model configuration
  model?: string;
  maxTokens?: number;
  temperature?: number;
  
  // Tool configuration
  approvalMode?: ApprovalMode;
  editTool?: EditTool;
  shellTool?: ShellTool;
  writeFileTool?: WriteFileTool;
  
  // Sandbox configuration
  sandboxEnabled?: boolean;
  sandboxImage?: string;
  
  // Debug and logging
  debugMode?: boolean;
  telemetryEnabled?: boolean;
  
  // UI configuration
  theme?: string;
  colorMode?: 'auto' | 'light' | 'dark';
  
  // Memory and context
  memoryEnabled?: boolean;
  maxContextSize?: number;
  
  // Extensions
  extensions?: string[];
  mcpServers?: Array<{
    name: string;
    command: string;
    args?: string[];
  }>;
}

export class Config {
  private data: ConfigData;
  private configPaths: string[];

  constructor(configData?: ConfigData) {
    this.configPaths = this.getConfigPaths();
    this.data = this.loadConfig(configData);
  }

  private getConfigPaths(): string[] {
    const homeDir = os.homedir();
    const cwd = process.cwd();
    
    return [
      path.join(cwd, '.arien.json'),
      path.join(cwd, '.arien', 'config.json'),
      path.join(homeDir, '.arien', 'config.json'),
      path.join(homeDir, '.config', 'arien', 'config.json'),
    ];
  }

  private loadConfig(override?: ConfigData): ConfigData {
    let config: ConfigData = {};
    
    // Load from config files (in reverse order, so later ones override earlier ones)
    for (const configPath of this.configPaths.reverse()) {
      if (existsSync(configPath)) {
        try {
          const content = readFileSync(configPath, 'utf8');
          const fileConfig = JSON.parse(stripJsonComments(content));
          config = { ...config, ...fileConfig };
        } catch (error) {
          console.warn(`Warning: Failed to load config from ${configPath}:`, error);
        }
      }
    }
    
    // Apply environment variable overrides
    config = this.applyEnvironmentOverrides(config);
    
    // Apply any direct overrides
    if (override) {
      config = { ...config, ...override };
    }
    
    return config;
  }

  private applyEnvironmentOverrides(config: ConfigData): ConfigData {
    const envOverrides: ConfigData = {};
    
    // Authentication
    if (process.env.ARIEN_API_KEY) {
      envOverrides.apiKey = process.env.ARIEN_API_KEY;
      envOverrides.authType = 'api-key';
    }
    
    if (process.env.ARIEN_SERVICE_ACCOUNT_PATH) {
      envOverrides.serviceAccountPath = process.env.ARIEN_SERVICE_ACCOUNT_PATH;
      envOverrides.authType = 'service-account';
    }
    
    // Model configuration
    if (process.env.ARIEN_MODEL) {
      envOverrides.model = process.env.ARIEN_MODEL;
    }
    
    if (process.env.ARIEN_MAX_TOKENS) {
      envOverrides.maxTokens = parseInt(process.env.ARIEN_MAX_TOKENS, 10);
    }
    
    if (process.env.ARIEN_TEMPERATURE) {
      envOverrides.temperature = parseFloat(process.env.ARIEN_TEMPERATURE);
    }
    
    // Tool configuration
    if (process.env.ARIEN_APPROVAL_MODE) {
      envOverrides.approvalMode = process.env.ARIEN_APPROVAL_MODE as ApprovalMode;
    }
    
    // Sandbox
    if (process.env.ARIEN_SANDBOX !== undefined) {
      envOverrides.sandboxEnabled = process.env.ARIEN_SANDBOX === 'true';
    }
    
    if (process.env.ARIEN_SANDBOX_IMAGE) {
      envOverrides.sandboxImage = process.env.ARIEN_SANDBOX_IMAGE;
    }
    
    // Debug and logging
    if (process.env.DEBUG) {
      envOverrides.debugMode = process.env.DEBUG === '1' || process.env.DEBUG === 'true';
    }
    
    if (process.env.ARIEN_TELEMETRY !== undefined) {
      envOverrides.telemetryEnabled = process.env.ARIEN_TELEMETRY === 'true';
    }
    
    // UI
    if (process.env.ARIEN_THEME) {
      envOverrides.theme = process.env.ARIEN_THEME;
    }
    
    if (process.env.ARIEN_COLOR_MODE) {
      envOverrides.colorMode = process.env.ARIEN_COLOR_MODE as 'auto' | 'light' | 'dark';
    }
    
    return { ...config, ...envOverrides };
  }

  // Getters for configuration values with defaults
  getAuthType(): AuthType {
    return this.data.authType || 'oauth';
  }

  getApiKey(): string | undefined {
    return this.data.apiKey;
  }

  getServiceAccountPath(): string | undefined {
    return this.data.serviceAccountPath;
  }

  getModel(): string {
    return this.data.model || 'gemini-1.5-flash';
  }

  getMaxTokens(): number {
    return this.data.maxTokens || 8192;
  }

  getTemperature(): number {
    return this.data.temperature || 0.7;
  }

  getApprovalMode(): ApprovalMode {
    return this.data.approvalMode || 'manual';
  }

  getEditTool(): EditTool {
    return this.data.editTool || {
      name: 'default',
      command: process.env.EDITOR || 'nano',
    };
  }

  getShellTool(): ShellTool {
    return this.data.shellTool || {
      name: 'default',
      command: process.platform === 'win32' ? 'cmd' : 'bash',
    };
  }

  getWriteFileTool(): WriteFileTool {
    return this.data.writeFileTool || {
      enabled: true,
      maxFileSize: 1024 * 1024, // 1MB
    };
  }

  isSandboxEnabled(): boolean {
    return this.data.sandboxEnabled ?? false;
  }

  getSandboxImage(): string {
    return this.data.sandboxImage || 'us-docker.pkg.dev/arien-code-dev/arien-cli/sandbox:1.0.0';
  }

  getDebugMode(): boolean {
    return this.data.debugMode ?? false;
  }

  isTelemetryEnabled(): boolean {
    return this.data.telemetryEnabled ?? true;
  }

  getTheme(): string {
    return this.data.theme || 'default';
  }

  getColorMode(): 'auto' | 'light' | 'dark' {
    return this.data.colorMode || 'auto';
  }

  isMemoryEnabled(): boolean {
    return this.data.memoryEnabled ?? true;
  }

  getMaxContextSize(): number {
    return this.data.maxContextSize || 1000000; // 1M tokens
  }

  getExtensions(): string[] {
    return this.data.extensions || [];
  }

  getMcpServers(): Array<{ name: string; command: string; args?: string[] }> {
    return this.data.mcpServers || [];
  }

  // Update configuration
  updateConfig(updates: Partial<ConfigData>): void {
    this.data = { ...this.data, ...updates };
  }

  // Get raw config data
  getRawConfig(): ConfigData {
    return { ...this.data };
  }
}

// Global config instance
let globalConfig: Config | null = null;

export function getConfig(): Config {
  if (!globalConfig) {
    globalConfig = new Config();
  }
  return globalConfig;
}

export function setConfig(config: Config): void {
  globalConfig = config;
}

export function loadCliConfig(configData?: ConfigData): Config {
  const config = new Config(configData);
  setConfig(config);
  return config;
}
