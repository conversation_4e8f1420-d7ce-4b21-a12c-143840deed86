import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */
import { useState, useEffect } from 'react';
import { Box, Text } from 'ink';
import { useTheme } from '../ui/ThemeProvider.js';
import { LoadingSpinner } from '../ui/LoadingSpinner.js';
import { ErrorDisplay } from '../ui/ErrorDisplay.js';
export const ConfigCommand = ({ config, args, telemetry, logger, onExit, }) => {
    const theme = useTheme();
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState(null);
    const [result, setResult] = useState(null);
    useEffect(() => {
        const executeCommand = async () => {
            try {
                setIsLoading(true);
                telemetry.logEvent({
                    name: 'config.command',
                    properties: {
                        subcommand: args.subcommand,
                        args: args.positional,
                    },
                });
                switch (args.subcommand) {
                    case 'show':
                        await handleShow();
                        break;
                    case 'set':
                        await handleSet();
                        break;
                    case 'get':
                        await handleGet();
                        break;
                    case 'list':
                        await handleList();
                        break;
                    case 'reset':
                        await handleReset();
                        break;
                    default:
                        await handleShow(); // Default to show
                        break;
                }
            }
            catch (err) {
                const errorMessage = err instanceof Error ? err.message : String(err);
                logger.error('Config command failed', { error: errorMessage }, 'ConfigCommand');
                setError(errorMessage);
            }
            finally {
                setIsLoading(false);
            }
        };
        executeCommand();
    }, [args.subcommand, args.positional, config, logger, telemetry]);
    const handleShow = async () => {
        const configData = {
            model: config.getModel(),
            approvalMode: config.getApprovalMode(),
            sandbox: config.isSandboxEnabled(),
            telemetry: config.isTelemetryEnabled(),
            theme: config.getTheme?.() || 'default',
        };
        setResult(`Current Configuration:
${Object.entries(configData)
            .map(([key, value]) => `  ${key}: ${value}`)
            .join('\n')}`);
    };
    const handleSet = async () => {
        const [key, value] = args.positional;
        if (!key || !value) {
            throw new Error('Usage: arien config set <key> <value>');
        }
        // TODO: Implement config setting
        setResult(`Configuration updated: ${key} = ${value}`);
        logger.info('Configuration updated', { key, value }, 'ConfigCommand');
    };
    const handleGet = async () => {
        const [key] = args.positional;
        if (!key) {
            throw new Error('Usage: arien config get <key>');
        }
        // TODO: Implement config getting
        setResult(`${key}: (value would be shown here)`);
    };
    const handleList = async () => {
        const configKeys = [
            'model',
            'approvalMode',
            'sandbox',
            'telemetry',
            'theme',
            'apiKey',
            'workingDirectory',
        ];
        setResult(`Available configuration keys:
${configKeys.map(key => `  ${key}`).join('\n')}`);
    };
    const handleReset = async () => {
        // TODO: Implement config reset
        setResult('Configuration reset to defaults');
        logger.info('Configuration reset', {}, 'ConfigCommand');
    };
    // Auto-exit after showing result
    useEffect(() => {
        if (result && !isLoading) {
            const timer = setTimeout(() => {
                onExit();
            }, 3000);
            return () => clearTimeout(timer);
        }
        return () => { }; // Return empty cleanup function for other cases
    }, [result, isLoading, onExit]);
    if (isLoading) {
        return (_jsx(Box, { padding: 1, children: _jsx(LoadingSpinner, { text: "Processing configuration..." }) }));
    }
    if (error) {
        return (_jsx(Box, { padding: 1, children: _jsx(ErrorDisplay, { error: error, onRetry: () => setError(null) }) }));
    }
    if (result) {
        return (_jsxs(Box, { flexDirection: "column", padding: 1, children: [_jsxs(Text, { color: theme.colors.success, children: [theme.symbols.success, " Configuration"] }), _jsx(Box, { marginTop: 1, children: _jsx(Text, { children: result }) })] }));
    }
    return (_jsx(Box, { padding: 1, children: _jsx(Text, { color: theme.colors.textSecondary, children: "No result" }) }));
};
