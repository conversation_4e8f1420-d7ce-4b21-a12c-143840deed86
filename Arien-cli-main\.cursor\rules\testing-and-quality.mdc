---
description: 
globs: 
alwaysApply: true
---
# Testing and Quality Assurance Guide

## Testing Framework

### Unit Testing
- **Test Framework**: Vitest for all unit tests
- **CLI Tests**: [packages/cli/src/](mdc:packages/cli/src) - Unit tests alongside source files
- **Core Tests**: [packages/core/src/](mdc:packages/core/src) - Core package unit tests
- **Test Configuration**: [packages/cli/vitest.config.ts](mdc:packages/cli/vitest.config.ts) and [packages/core/vitest.config.ts](mdc:packages/core/vitest.config.ts)

### Integration Testing
- **Integration Suite**: [integration-tests/](mdc:integration-tests) - End-to-end test scenarios
- **Test Runner**: [integration-tests/run-tests.js](mdc:integration-tests/run-tests.js) - Integration test orchestration
- **Test Helper**: [integration-tests/test-helper.js](mdc:integration-tests/test-helper.js) - Common test utilities

### Specific Integration Tests
- **File System**: [integration-tests/file-system.test.js](mdc:integration-tests/file-system.test.js) - File operation testing
- **Shell Commands**: [integration-tests/run_shell_command.test.js](mdc:integration-tests/run_shell_command.test.js) - Command execution tests
- **Web Search**: [integration-tests/google_web_search.test.js](mdc:integration-tests/google_web_search.test.js) - Search functionality tests
- **Memory System**: [integration-tests/save_memory.test.js](mdc:integration-tests/save_memory.test.js) - Memory persistence tests
- **MCP Integration**: [integration-tests/simple-mcp-server.test.js](mdc:integration-tests/simple-mcp-server.test.js) - MCP server tests

## Test Categories

### Unit Test Examples
- **Component Tests**: React component testing with Testing Library
- **Hook Tests**: Custom React hook testing
- **Utility Tests**: Pure function and utility testing
- **Configuration Tests**: Config validation and loading tests

### Integration Test Scenarios
- **File Operations**: Read, write, search, and multi-file operations
- **Tool Execution**: Complete tool execution workflows
- **Authentication**: Auth flow testing
- **Sandbox Testing**: Containerized execution testing

## Testing NPM Scripts

### Development Testing
- `npm run test` - Run all unit tests across workspaces
- `npm run test:ci` - CI test run with coverage reporting
- `npm run typecheck` - TypeScript type checking

### Integration Testing
- `npm run test:e2e` - End-to-end test suite
- `npm run test:integration:all` - Complete integration test matrix
- `npm run test:integration:sandbox:none` - Tests without sandbox
- `npm run test:integration:sandbox:docker` - Docker sandbox tests
- `npm run test:integration:sandbox:podman` - Podman sandbox tests

## Code Quality Tools

### Linting and Formatting
- **ESLint Configuration**: [eslint.config.js](mdc:eslint.config.js) - Comprehensive linting rules
- **Custom ESLint Rules**: [eslint-rules/](mdc:eslint-rules) - Project-specific rules
- **Prettier Configuration**: [.prettierrc.json](mdc:.prettierrc.json) - Code formatting standards
- **EditorConfig**: [.editorconfig](mdc:.editorconfig) - Editor configuration

### Quality Scripts
- `npm run lint` - Run ESLint checks
- `npm run lint:fix` - Auto-fix linting issues
- `npm run lint:ci` - CI linting with zero warnings
- `npm run format` - Format all code with Prettier

### TypeScript Configuration
- **Root TypeScript**: [tsconfig.json](mdc:tsconfig.json) - Main TypeScript configuration
- **Package Configs**: Individual TypeScript configs per package
- **Strict Checking**: Comprehensive type checking enabled

## Test Snapshots and Fixtures

### Component Snapshots
- **CLI Snapshots**: [packages/cli/src/ui/components/__snapshots__/](mdc:packages/cli/src/ui/components/__snapshots__) - React component snapshots
- **Core Snapshots**: [packages/core/src/core/__snapshots__/](mdc:packages/core/src/core/__snapshots__) - Core logic snapshots

### Test Utilities
- **Mocks**: [packages/core/src/__mocks__/](mdc:packages/core/src/__mocks__) - Mock implementations
- **Test Setup**: [packages/core/test-setup.ts](mdc:packages/core/test-setup.ts) - Global test configuration

## Coverage and Reporting

### Coverage Configuration
- **Vitest Coverage**: Using @vitest/coverage-v8 for coverage reporting
- **Coverage Thresholds**: Configured in vitest.config.ts files
- **CI Coverage**: Automatic coverage reporting in CI

### Quality Metrics
- **Type Safety**: 100% TypeScript coverage
- **Linting**: Zero ESLint warnings in CI
- **Test Coverage**: Comprehensive unit and integration coverage

## Continuous Integration

### CI Pipeline Features (from [package.json](mdc:package.json))
- **Preflight Check**: `npm run preflight` - Complete quality check
- **Build Validation**: Ensure all packages build successfully
- **Test Matrix**: Multiple test environments and configurations
- **Quality Gates**: Linting, formatting, and type checking

### Pre-commit Hooks
- **Code Formatting**: Automatic Prettier formatting
- **Linting**: ESLint validation
- **Type Checking**: TypeScript compilation check

## Testing Documentation

### Testing Guides
- **Integration Tests**: [docs/integration-tests.md](mdc:docs/integration-tests.md) - Integration testing guide
- **Test Writing**: Best practices for writing tests
- **Troubleshooting**: [docs/troubleshooting.md](mdc:docs/troubleshooting.md) - Common testing issues

### Quality Standards
- **Code Review**: Standards for code review process
- **Test Requirements**: Minimum test coverage expectations
- **Documentation**: Test documentation requirements

