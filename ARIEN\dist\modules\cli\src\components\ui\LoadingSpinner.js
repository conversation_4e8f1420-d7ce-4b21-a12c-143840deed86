import { jsxs as _jsxs, jsx as _jsx } from "react/jsx-runtime";
/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */
import { useState, useEffect } from 'react';
import { Box, Text } from 'ink';
import { useTheme } from './ThemeProvider.js';
const spinnerFrames = {
    dots: ['⠋', '⠙', '⠹', '⠸', '⠼', '⠴', '⠦', '⠧', '⠇', '⠏'],
    line: ['|', '/', '-', '\\'],
    bounce: ['⠁', '⠂', '⠄', '⠂'],
    pulse: ['●', '○', '●', '○'],
};
export const LoadingSpinner = ({ text = 'Loading...', type = 'dots', color, }) => {
    const theme = useTheme();
    const [frame, setFrame] = useState(0);
    const frames = spinnerFrames[type];
    const spinnerColor = color || theme.colors.primary;
    useEffect(() => {
        const interval = setInterval(() => {
            setFrame(prevFrame => (prevFrame + 1) % frames.length);
        }, 100);
        return () => clearInterval(interval);
    }, [frames.length]);
    return (_jsxs(Box, { children: [_jsxs(Text, { color: spinnerColor, children: [frames[frame], " "] }), _jsx(Text, { children: text })] }));
};
