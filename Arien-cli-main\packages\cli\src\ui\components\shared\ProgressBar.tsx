/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import React from 'react';
import { Box, Text } from 'ink';
import { Colors } from '../../colors.js';

interface ProgressBarProps {
  progress: number; // 0-100
  width?: number;
  showPercentage?: boolean;
  color?: string;
  backgroundColor?: string;
  character?: string;
}

export const ProgressBar: React.FC<ProgressBarProps> = ({
  progress,
  width = 20,
  showPercentage = true,
  color = Colors.AccentCyan,
  backgroundColor = Colors.Gray,
  character = '█',
}) => {
  const clampedProgress = Math.max(0, Math.min(100, progress));
  const filledWidth = Math.floor((clampedProgress / 100) * width);
  const emptyWidth = width - filledWidth;

  return (
    <Box>
      <Text color={backgroundColor}>『</Text>
      <Text color={color}>{character.repeat(filledWidth)}</Text>
      <Text color={backgroundColor} dimColor>
        {'░'.repeat(emptyWidth)}
      </Text>
      <Text color={backgroundColor}>』</Text>
      {showPercentage && (
        <Text color={color}> {clampedProgress}%</Text>
      )}
    </Box>
  );
};

interface IndeterminateProgressProps {
  width?: number;
  color?: string;
  animationFrame?: number;
}

export const IndeterminateProgress: React.FC<IndeterminateProgressProps> = ({
  width = 20,
  color = Colors.AccentPurple,
  animationFrame = 0,
}) => {
  const position = animationFrame % (width + 4);
  const barPosition = Math.max(0, Math.min(position - 2, width - 3));
  
  const bar = Array(width)
    .fill('░')
    .map((char, i) => {
      if (i >= barPosition && i < barPosition + 3 && position >= 2 && position < width + 2) {
        return '█';
      }
      return char;
    })
    .join('');

  return (
    <Box>
      <Text color={Colors.Gray}>『</Text>
      <Text color={color}>{bar}</Text>
      <Text color={Colors.Gray}>』</Text>
    </Box>
  );
}; 