/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { Box, Text } from 'ink';
import { Colors } from '../colors.js';
export interface Suggestion {
  label: string;
  value: string;
  description?: string;
}
interface SuggestionsDisplayProps {
  suggestions: Suggestion[];
  activeIndex: number;
  isLoading: boolean;
  width: number;
  scrollOffset: number;
  userInput: string;
}

export const MAX_SUGGESTIONS_TO_SHOW = 8;

export function SuggestionsDisplay({
  suggestions,
  activeIndex,
  isLoading,
  width,
  scrollOffset,
  userInput,
}: SuggestionsDisplayProps) {
  if (isLoading) {
    return (
      <Box paddingX={1} width={width} borderStyle="round" borderColor={Colors.Gray}>
        <Text color={Colors.Gray}>
          <Text color={Colors.AccentCyan}>◎ </Text>
          Loading suggestions...
        </Text>
      </Box>
    );
  }

  if (suggestions.length === 0) {
    return null; // Don't render anything if there are no suggestions
  }

  // Calculate the visible slice based on scrollOffset
  const startIndex = scrollOffset;
  const endIndex = Math.min(
    scrollOffset + MAX_SUGGESTIONS_TO_SHOW,
    suggestions.length,
  );
  const visibleSuggestions = suggestions.slice(startIndex, endIndex);

  return (
    <Box flexDirection="column" paddingX={1} width={width} borderStyle="round" borderColor={Colors.AccentCyan}>
      {scrollOffset > 0 && (
        <Box marginBottom={0}>
          <Text color={Colors.AccentCyan}>▲ More above</Text>
        </Box>
      )}

      {visibleSuggestions.map((suggestion, index) => {
        const originalIndex = startIndex + index;
        const isActive = originalIndex === activeIndex;
        const textColor = isActive ? Colors.AccentPurple : Colors.Gray;
        const prefix = isActive ? '→ ' : '  ';

        return (
          <Box key={`${suggestion}-${originalIndex}`} width={width}>
            <Box flexDirection="row">
              <Text color={textColor}>{prefix}</Text>
              {userInput.startsWith('/') ? (
                // only use box model for (/) command mode
                <Box width={20} flexShrink={0}>
                  <Text color={textColor} bold={isActive}>{suggestion.label}</Text>
                </Box>
              ) : (
                // use regular text for other modes (@ context)
                <Text color={textColor} bold={isActive}>{suggestion.label}</Text>
              )}
              {suggestion.description ? (
                <Box flexGrow={1} marginLeft={1}>
                  <Text color={textColor} dimColor={!isActive} wrap="wrap">
                    {suggestion.description}
                  </Text>
                </Box>
              ) : null}
            </Box>
          </Box>
        );
      })}
      {endIndex < suggestions.length && (
        <Box marginTop={0}>
          <Text color={Colors.AccentCyan}>▼ More below</Text>
        </Box>
      )}
      {suggestions.length > MAX_SUGGESTIONS_TO_SHOW && (
        <Box marginTop={0}>
          <Text color={Colors.Gray} dimColor>
            {activeIndex + 1} of {suggestions.length} · Tab to select
          </Text>
        </Box>
      )}
    </Box>
  );
}
