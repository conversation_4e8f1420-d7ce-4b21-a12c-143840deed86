/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */
import { sessionId } from '../utils/session.js';
export class MetricsCollector {
    metrics = [];
    counters = new Map();
    gauges = new Map();
    timers = new Map();
    enabled;
    maxMetrics = 10000;
    constructor(enabled = true) {
        this.enabled = enabled;
    }
    /**
     * Record a metric
     */
    recordMetric(metric) {
        if (!this.enabled)
            return;
        this.metrics.push(metric);
        // Keep metrics under control
        if (this.metrics.length > this.maxMetrics) {
            this.metrics.splice(0, this.metrics.length - this.maxMetrics);
        }
    }
    /**
     * Increment a counter
     */
    incrementCounter(name, value = 1, tags) {
        if (!this.enabled)
            return;
        const key = this.getMetricKey(name, tags);
        const currentValue = this.counters.get(key) || 0;
        const newValue = currentValue + value;
        this.counters.set(key, newValue);
        const metric = {
            name,
            type: 'counter',
            value: newValue,
            increment: value,
            timestamp: new Date(),
            sessionId,
            tags,
        };
        this.recordMetric(metric);
    }
    /**
     * Record a gauge value
     */
    recordGauge(name, value, tags) {
        if (!this.enabled)
            return;
        const key = this.getMetricKey(name, tags);
        this.gauges.set(key, value);
        const metric = {
            name,
            type: 'gauge',
            value,
            timestamp: new Date(),
            sessionId,
            tags,
        };
        this.recordMetric(metric);
    }
    /**
     * Start a timer
     */
    startTimer(name, tags) {
        if (!this.enabled)
            return () => { };
        const key = this.getMetricKey(name, tags);
        const startTime = new Date();
        this.timers.set(key, { startTime, name });
        return () => {
            const timer = this.timers.get(key);
            if (!timer)
                return;
            const endTime = new Date();
            const duration = endTime.getTime() - timer.startTime.getTime();
            const metric = {
                name,
                type: 'timer',
                value: duration,
                duration,
                startTime: timer.startTime,
                endTime,
                timestamp: endTime,
                sessionId,
                tags,
                unit: 'ms',
            };
            this.recordMetric(metric);
            this.timers.delete(key);
        };
    }
    /**
     * Record a histogram value
     */
    recordHistogram(name, value, buckets, tags) {
        if (!this.enabled)
            return;
        // Find the appropriate bucket
        const bucketCounts = {};
        for (const bucket of buckets) {
            const bucketKey = bucket.toString();
            bucketCounts[bucketKey] = value <= bucket ? 1 : 0;
        }
        const metric = {
            name,
            type: 'histogram',
            value,
            timestamp: new Date(),
            sessionId,
            tags,
        };
        this.recordMetric(metric);
    }
    /**
     * Get all recorded metrics
     */
    getMetrics() {
        return [...this.metrics];
    }
    /**
     * Get metrics by type
     */
    getMetricsByType(type) {
        return this.metrics.filter(metric => metric.type === type);
    }
    /**
     * Get metrics by name
     */
    getMetricsByName(name) {
        return this.metrics.filter(metric => metric.name === name);
    }
    /**
     * Get current counter values
     */
    getCounters() {
        return new Map(this.counters);
    }
    /**
     * Get current gauge values
     */
    getGauges() {
        return new Map(this.gauges);
    }
    /**
     * Get active timers
     */
    getActiveTimers() {
        return Array.from(this.timers.keys());
    }
    /**
     * Clear all metrics
     */
    clear() {
        this.metrics.length = 0;
        this.counters.clear();
        this.gauges.clear();
        this.timers.clear();
    }
    /**
     * Enable or disable metrics collection
     */
    setEnabled(enabled) {
        this.enabled = enabled;
    }
    /**
     * Check if metrics collection is enabled
     */
    isEnabled() {
        return this.enabled;
    }
    /**
     * Set the maximum number of metrics to keep
     */
    setMaxMetrics(max) {
        this.maxMetrics = max;
        // Trim existing metrics if necessary
        if (this.metrics.length > max) {
            this.metrics.splice(0, this.metrics.length - max);
        }
    }
    /**
     * Get metrics summary
     */
    getSummary() {
        const metricsByType = this.metrics.reduce((acc, metric) => {
            acc[metric.type] = (acc[metric.type] || 0) + 1;
            return acc;
        }, {});
        return {
            totalMetrics: this.metrics.length,
            counters: metricsByType.counter || 0,
            gauges: metricsByType.gauge || 0,
            timers: metricsByType.timer || 0,
            histograms: metricsByType.histogram || 0,
            activeTimers: this.timers.size,
        };
    }
    /**
     * Generate a unique key for a metric with tags
     */
    getMetricKey(name, tags) {
        if (!tags || Object.keys(tags).length === 0) {
            return name;
        }
        const sortedTags = Object.entries(tags)
            .sort(([a], [b]) => a.localeCompare(b))
            .map(([key, value]) => `${key}=${value}`)
            .join(',');
        return `${name}{${sortedTags}}`;
    }
}
