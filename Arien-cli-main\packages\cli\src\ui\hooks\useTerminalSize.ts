/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { useEffect, useState } from 'react';

const TERMINAL_PADDING_X = 8;
const MIN_TERMINAL_WIDTH = 40;
const MIN_TERMINAL_HEIGHT = 10;
const DEFAULT_TERMINAL_WIDTH = 80;
const DEFAULT_TERMINAL_HEIGHT = 24;

export function useTerminalSize(): { columns: number; rows: number } {
  const getOptimalSize = () => {
    const rawColumns = process.stdout.columns || DEFAULT_TERMINAL_WIDTH;
    const rawRows = process.stdout.rows || DEFAULT_TERMINAL_HEIGHT;
    
    return {
      columns: Math.max(rawColumns - TERMINAL_PADDING_X, MIN_TERMINAL_WIDTH),
      rows: Math.max(rawRows, MIN_TERMINAL_HEIGHT),
    };
  };

  const [size, setSize] = useState(getOptimalSize);

  useEffect(() => {
    function updateSize() {
      setSize(getOptimalSize());
    }

    // Debounce resize events to improve performance
    let resizeTimeout: NodeJS.Timeout;
    function debouncedUpdateSize() {
      clearTimeout(resizeTimeout);
      resizeTimeout = setTimeout(updateSize, 50);
    }

    process.stdout.on('resize', debouncedUpdateSize);
    return () => {
      process.stdout.off('resize', debouncedUpdateSize);
      clearTimeout(resizeTimeout);
    };
  }, []);

  return size;
}
