---
description: 
globs: 
alwaysApply: true
---
# Tools and Integrations Guide

## Core Tools System

### File System Tools
- **File Operations**: [packages/core/src/tools/fileSystemTools.ts](mdc:packages/core/src/tools/fileSystemTools.ts) - Read, write, search files
- **Multi-File Operations**: [packages/core/src/tools/multiFileTools.ts](mdc:packages/core/src/tools/multiFileTools.ts) - Batch file processing
- **Directory Tools**: Directory listing, creation, and management
- **File Search**: Advanced file discovery with patterns and filters

### Shell Integration
- **Shell Commands**: [packages/core/src/tools/shellTools.ts](mdc:packages/core/src/tools/shellTools.ts) - Execute system commands
- **Command Validation**: Safety checks for shell execution
- **Output Streaming**: Real-time command output display
- **Environment Management**: Environment variable handling

### Web Capabilities
- **Web Fetch**: [packages/core/src/tools/webFetchTool.ts](mdc:packages/core/src/tools/webFetchTool.ts) - HTTP requests and web scraping
- **Web Search**: [packages/core/src/tools/webSearchTool.ts](mdc:packages/core/src/tools/webSearchTool.ts) - Google Search integration
- **URL Processing**: Web content extraction and processing

## MCP (Model Context Protocol) Integration

### MCP Server Support
- **MCP Tools**: [packages/core/src/tools/mcpTools.ts](mdc:packages/core/src/tools/mcpTools.ts) - MCP server integration
- **Server Discovery**: Automatic MCP server detection and connection
- **Protocol Handling**: MCP message processing and routing
- **Documentation**: [docs/tools/mcp-server.md](mdc:docs/tools/mcp-server.md) - MCP integration guide

### Available MCP Servers
- Custom MCP servers for domain-specific tools
- Third-party MCP server integration
- Dynamic tool registration from MCP servers

## Memory and State Management

### Memory Tools
- **Memory System**: [packages/core/src/tools/memoryTool.ts](mdc:packages/core/src/tools/memoryTool.ts) - Conversation memory management
- **State Persistence**: Long-term conversation state
- **Context Management**: Large context window optimization
- **Documentation**: [docs/tools/memory.md](mdc:docs/tools/memory.md) - Memory system guide

## Advanced Tool Features

### Tool Execution Framework
- **Tool Registry**: Dynamic tool registration and discovery
- **Permission System**: User approval for dangerous operations
- **Validation**: Input validation and safety checks
- **Error Handling**: Robust error handling and recovery

### Tool Categories
1. **Read-Only Tools**: File reading, web fetching (auto-approved)
2. **Write Tools**: File modification, shell commands (require approval)
3. **System Tools**: System information, process management
4. **Integration Tools**: External API connections

## Sandbox Environment

### Sandboxing System
- **Sandbox Config**: [packages/cli/src/config/sandboxConfig.ts](mdc:packages/cli/src/config/sandboxConfig.ts) - Sandbox configuration
- **Docker Integration**: Containerized execution environment
- **Security Policies**: macOS sandbox profiles in [packages/cli/src/utils/](mdc:packages/cli/src/utils)
- **Documentation**: [docs/sandbox.md](mdc:docs/sandbox.md) - Sandbox system guide

### Sandbox Features
- **Isolated Execution**: Safe command execution in containers
- **File System Isolation**: Controlled file access
- **Network Controls**: Managed network access
- **Resource Limits**: CPU and memory constraints

## External Integrations

### Authentication Systems
- **Google Auth**: Google Workspace and personal account integration
- **API Keys**: Support for various API key authentication
- **OAuth Flows**: Standard OAuth 2.0 integration
- **Documentation**: [docs/cli/authentication.md](mdc:docs/cli/authentication.md) - Auth guide

### Service Integrations
- **Google AI**: Primary AI model provider
- **Google Search**: Built-in web search capabilities
- **GitHub**: Repository and issue management
- **Cloud Services**: Various cloud platform integrations

## Tool Documentation

### Individual Tool Guides
- **File System**: [docs/tools/file-system.md](mdc:docs/tools/file-system.md) - File operation details
- **Shell**: [docs/tools/shell.md](mdc:docs/tools/shell.md) - Shell command guide
- **Web Fetch**: [docs/tools/web-fetch.md](mdc:docs/tools/web-fetch.md) - Web request documentation
- **Web Search**: [docs/tools/web-search.md](mdc:docs/tools/web-search.md) - Search integration guide
- **Multi-File**: [docs/tools/multi-file.md](mdc:docs/tools/multi-file.md) - Batch operations

### Tool Development
- **Tools API**: [docs/core/tools-api.md](mdc:docs/core/tools-api.md) - Tool development guide
- **Custom Tools**: How to create and register custom tools
- **Tool Testing**: Testing framework for tool validation

## Safety and Security

### Security Measures
- **User Approval**: Explicit approval for potentially dangerous operations
- **Command Validation**: Input sanitization and validation
- **Sandbox Execution**: Isolated execution environments
- **Rate Limiting**: API and operation rate limiting

### Privacy Controls
- **Data Handling**: Secure data processing
- **Telemetry Controls**: Optional usage analytics
- **Local Processing**: Local-first operation where possible

