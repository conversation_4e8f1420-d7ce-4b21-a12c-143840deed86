import { HrTime, SpanAttributes } from '@opentelemetry/api';
/**
 * Represents a timed event.
 * A timed event is an event with a timestamp.
 */
export interface TimedEvent {
    time: HrTime;
    /** The name of the event. */
    name: string;
    /** The attributes of the event. */
    attributes?: SpanAttributes;
    /** Count of attributes of the event that were dropped due to collection limits */
    droppedAttributesCount?: number;
}
//# sourceMappingURL=TimedEvent.d.ts.map