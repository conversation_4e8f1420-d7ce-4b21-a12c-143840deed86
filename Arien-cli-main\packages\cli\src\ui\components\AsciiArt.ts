/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

const arienAscii = `
 █████╗ ██████╗ ██╗███████╗███╗   ██╗     █████╗ ██╗
██╔══██╗██╔══██╗██║██╔════╝████╗  ██║    ██╔══██╗██║
███████║██████╔╝██║█████╗  ██╔██╗ ██║    ███████║██║
██╔══██║██╔══██╗██║██╔══╝  ██║╚██╗██║    ██╔══██║██║
██║  ██║██║  ██║██║███████╗██║ ╚████║    ██║  ██║██║
╚═╝  ╚═╝╚═╝  ╚═╝╚═╝╚══════╝╚═╝  ╚═══╝    ╚═╝  ╚═╝╚═╝
`;

const morningAscii = arienAscii;
const afternoonAscii = arienAscii;
const eveningAscii = arienAscii;
const nightAscii = arienAscii;

function getGreetingBasedOnTime(): string {
  const hour = new Date().getHours();
  
  if (hour >= 5 && hour < 12) {
    return morningAscii;
  } else if (hour >= 12 && hour < 17) {
    return afternoonAscii;
  } else if (hour >= 17 && hour < 21) {
    return eveningAscii;
  } else {
    return nightAscii;
  }
}

export const shortAsciiLogo = getGreetingBasedOnTime();
export const longAsciiLogo = getGreetingBasedOnTime();
