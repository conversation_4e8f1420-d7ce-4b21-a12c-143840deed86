{"root": ["../modules/cli/index.d.ts", "../modules/cli/index.js", "../modules/cli/test-setup.ts", "../modules/cli/vitest.config.ts", "../modules/cli/src/index.ts", "../modules/cli/src/components/ariencli.tsx", "../modules/cli/src/components/chatinterface.tsx", "../modules/cli/src/components/commands/authcommand.tsx", "../modules/cli/src/components/commands/configcommand.tsx", "../modules/cli/src/components/commands/memorycommand.tsx", "../modules/cli/src/components/commands/toolscommand.tsx", "../modules/cli/src/components/ui/errordisplay.tsx", "../modules/cli/src/components/ui/inputprompt.tsx", "../modules/cli/src/components/ui/loadingspinner.tsx", "../modules/cli/src/components/ui/messagedisplay.tsx", "../modules/cli/src/components/ui/themeprovider.tsx", "../modules/cli/src/generated/git-commit.ts", "../modules/cli/src/utils/args.ts", "../modules/cli/src/utils/error-handling.ts", "../modules/cli/src/utils/system-check.ts", "../modules/core/index.d.ts", "../modules/core/index.js", "../modules/core/test-setup.ts", "../modules/core/vitest.config.ts", "../modules/core/src/index.ts", "../modules/core/src/config/config.ts", "../modules/core/src/config/models.ts", "../modules/core/src/core/client.ts", "../modules/core/src/core/logger.ts", "../modules/core/src/telemetry/index.ts", "../modules/core/src/telemetry/loggers.ts", "../modules/core/src/telemetry/metrics.ts", "../modules/core/src/telemetry/types.ts", "../modules/core/src/tools/initialize-tools.ts", "../modules/core/src/tools/read-file.ts", "../modules/core/src/tools/shell-execute.ts", "../modules/core/src/tools/tool-registry.ts", "../modules/core/src/tools/tools.ts", "../modules/core/src/tools/write-file.ts", "../modules/core/src/utils/errors.ts", "../modules/core/src/utils/paths.ts", "../modules/core/src/utils/retry.ts", "../modules/core/src/utils/session.ts", "../scripts/build-module.js", "../scripts/build.js", "../scripts/clean.js", "../scripts/copy-bundle-assets.js", "../scripts/generate-git-commit-info.js", "../scripts/start.js"], "errors": true, "version": "5.8.3"}