{"version": 3, "file": "MetricProducer.js", "sourceRoot": "", "sources": ["../../../src/export/MetricProducer.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { CollectionResult } from './MetricData';\n\nexport interface MetricCollectOptions {\n  /**\n   * Timeout for the SDK to perform the involved asynchronous callback\n   * functions.\n   *\n   * If the callback functions failed to finish the observation in time,\n   * their results are discarded and an error is appended in the\n   * {@link CollectionResult.errors}.\n   */\n  timeoutMillis?: number;\n}\n\n/**\n * This is a public interface that represent an export state of a MetricReader.\n */\nexport interface MetricProducer {\n  /**\n   * Collects the metrics from the SDK. If there are asynchronous Instruments\n   * involved, their callback functions will be triggered.\n   */\n  collect(options?: MetricCollectOptions): Promise<CollectionResult>;\n}\n"]}