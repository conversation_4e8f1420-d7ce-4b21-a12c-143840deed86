/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */

import { execSync } from 'child_process';
import { writeFileSync, mkdirSync } from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.dirname(__dirname);

/**
 * Generate git commit information for the build
 */
function generateGitCommitInfo() {
  console.log('📝 Generating git commit information...');
  
  let gitCommitHash = 'unknown';
  let gitCommitDate = 'unknown';
  let gitBranch = 'unknown';
  let gitTag = 'unknown';
  
  try {
    // Get git commit hash
    gitCommitHash = execSync('git rev-parse HEAD', { encoding: 'utf8' }).trim();
    
    // Get git commit date
    gitCommitDate = execSync('git log -1 --format=%ci', { encoding: 'utf8' }).trim();
    
    // Get git branch
    gitBranch = execSync('git rev-parse --abbrev-ref HEAD', { encoding: 'utf8' }).trim();
    
    // Get git tag (if any)
    try {
      gitTag = execSync('git describe --tags --exact-match', { encoding: 'utf8' }).trim();
    } catch {
      // No tag found, use commit hash
      gitTag = gitCommitHash.substring(0, 8);
    }
  } catch (error) {
    console.warn('⚠️  Git information not available:', error.message);
  }
  
  const gitInfo = {
    commitHash: gitCommitHash,
    commitDate: gitCommitDate,
    branch: gitBranch,
    tag: gitTag,
    buildDate: new Date().toISOString(),
  };
  
  // Ensure the generated directory exists
  const generatedDir = path.join(rootDir, 'modules/cli/src/generated');
  mkdirSync(generatedDir, { recursive: true });
  
  // Write git commit info
  const gitCommitPath = path.join(generatedDir, 'git-commit.ts');
  const gitCommitContent = `/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */

// This file is auto-generated by scripts/generate-git-commit-info.js
// Do not edit manually

export const gitCommitInfo = ${JSON.stringify(gitInfo, null, 2)} as const;

export const getVersionInfo = () => ({
  version: process.env.CLI_VERSION || '1.0.0',
  ...gitCommitInfo,
});
`;
  
  writeFileSync(gitCommitPath, gitCommitContent);
  
  console.log('✅ Git commit information generated');
  console.log(`   Commit: ${gitCommitHash.substring(0, 8)}`);
  console.log(`   Branch: ${gitBranch}`);
  console.log(`   Tag: ${gitTag}`);
}

// Run the generation
generateGitCommitInfo();
