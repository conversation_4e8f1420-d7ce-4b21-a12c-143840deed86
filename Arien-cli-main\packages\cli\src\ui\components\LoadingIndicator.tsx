/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { ThoughtSummary } from '@arien/arien-cli-core';
import React from 'react';
import { Box, Text } from 'ink';
import { Colors } from '../colors.js';
import { useStreamingContext } from '../contexts/StreamingContext.js';
import { StreamingState } from '../types.js';
import { ArienRespondingSpinner } from './ArienRespondingSpinner.js';

interface LoadingIndicatorProps {
  currentLoadingPhrase?: string;
  elapsedTime: number;
  rightContent?: React.ReactNode;
  thought?: ThoughtSummary | null;
}

export const LoadingIndicator: React.FC<LoadingIndicatorProps> = ({
  currentLoadingPhrase,
  elapsedTime,
  rightContent,
  thought,
}) => {
  const streamingState = useStreamingContext();

  if (streamingState === StreamingState.Idle) {
    return null;
  }

  const primaryText = thought?.subject || currentLoadingPhrase;
  
  // Enhanced loading state feedback
  const getLoadingState = () => {
    switch (streamingState) {
      case StreamingState.WaitingForConfirmation:
        return 'Waiting for confirmation';
      case StreamingState.Responding:
        return 'Processing response';
      default:
        return 'Loading';
    }
  };

  // Progress indicator based on elapsed time
  const getProgressIndicator = () => {
    if (elapsedTime < 2) return '';
    if (elapsedTime < 5) return ' •';
    if (elapsedTime < 10) return ' ••';
    if (elapsedTime < 20) return ' •••';
    return ' ••••';
  };

  return (
    <Box marginTop={1} paddingLeft={0} flexDirection="column">
      {/* Main loading line */}
      <Box>
        <Box marginRight={1}>
          <ArienRespondingSpinner
            nonRespondingDisplay={
              streamingState === StreamingState.WaitingForConfirmation
                ? '⠏'
                : ''
            }
          />
        </Box>
        {primaryText ? (
          <Text color={Colors.AccentPurple}>{primaryText}{getProgressIndicator()}</Text>
        ) : (
          <Text color={Colors.AccentPurple}>{getLoadingState()}{getProgressIndicator()}</Text>
        )}
        <Text color={Colors.Gray}>
          {streamingState === StreamingState.WaitingForConfirmation
            ? ' (awaiting input)'
            : ` (esc to cancel, ${elapsedTime}s)`}
        </Text>
        <Box flexGrow={1}>{/* Spacer */}</Box>
        {rightContent && <Box>{rightContent}</Box>}
      </Box>
      
      {/* Enhanced progress feedback for longer operations */}
      {elapsedTime > 10 && streamingState !== StreamingState.WaitingForConfirmation && (
        <Box marginTop={0} paddingLeft={3}>
          <Text color={Colors.Gray} dimColor>
            {elapsedTime > 30 
              ? 'This is taking longer than usual. The operation is still running...'
              : 'Processing complex request...'
            }
          </Text>
        </Box>
      )}
    </Box>
  );
};
