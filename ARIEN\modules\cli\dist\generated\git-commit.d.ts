/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */
export declare const gitCommitInfo: {
    readonly commitHash: "unknown";
    readonly commitDate: "unknown";
    readonly branch: "unknown";
    readonly tag: "unknown";
    readonly buildDate: "2025-07-02T05:24:23.720Z";
};
export declare const getVersionInfo: () => {
    commitHash: "unknown";
    commitDate: "unknown";
    branch: "unknown";
    tag: "unknown";
    buildDate: "2025-07-02T05:24:23.720Z";
    version: string;
};
//# sourceMappingURL=git-commit.d.ts.map