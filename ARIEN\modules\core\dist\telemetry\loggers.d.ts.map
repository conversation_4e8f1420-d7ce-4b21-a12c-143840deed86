{"version": 3, "file": "loggers.d.ts", "sourceRoot": "", "sources": ["../../src/telemetry/loggers.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAGH,OAAO,EAAE,cAAc,EAAE,MAAM,YAAY,CAAC;AAE5C,qBAAa,eAAe;IAC1B,OAAO,CAAC,MAAM,CAAwB;IACtC,OAAO,CAAC,OAAO,CAAU;IACzB,OAAO,CAAC,SAAS,CAAS;gBAEd,OAAO,UAAO;IAI1B;;OAEG;IACH,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC,cAAc,EAAE,WAAW,GAAG,WAAW,CAAC,GAAG,IAAI;IAiBtE;;OAEG;IACH,SAAS,IAAI,cAAc,EAAE;IAI7B;;OAEG;IACH,eAAe,CAAC,IAAI,EAAE,MAAM,GAAG,cAAc,EAAE;IAI/C;;OAEG;IACH,oBAAoB,CAAC,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,GAAG,cAAc,EAAE;IAMtE;;OAEG;IACH,kBAAkB,CAAC,SAAS,EAAE,MAAM,GAAG,cAAc,EAAE;IAIvD;;OAEG;IACH,eAAe,CAAC,MAAM,EAAE,MAAM,GAAG,cAAc,EAAE;IAIjD;;OAEG;IACH,eAAe,CAAC,IAAI,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG,cAAc,EAAE;IAU/D;;OAEG;IACH,KAAK,IAAI,IAAI;IAIb;;OAEG;IACH,UAAU,CAAC,OAAO,EAAE,OAAO,GAAG,IAAI;IAIlC;;OAEG;IACH,SAAS,IAAI,OAAO;IAIpB;;OAEG;IACH,YAAY,CAAC,GAAG,EAAE,MAAM,GAAG,IAAI;IAS/B;;OAEG;IACH,aAAa,IAAI;QACf,WAAW,EAAE,MAAM,CAAC;QACpB,YAAY,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QACrC,eAAe,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QACxC,YAAY,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QACrC,SAAS,EAAE;YAAE,KAAK,CAAC,EAAE,IAAI,CAAC;YAAC,GAAG,CAAC,EAAE,IAAI,CAAA;SAAE,CAAC;KACzC;IAqCD;;OAEG;IACH,YAAY,IAAI;QACd,MAAM,EAAE,cAAc,EAAE,CAAC;QACzB,QAAQ,EAAE;YACR,UAAU,EAAE,IAAI,CAAC;YACjB,WAAW,EAAE,MAAM,CAAC;YACpB,SAAS,EAAE,MAAM,CAAC;SACnB,CAAC;KACH;IAWD;;OAEG;IACH,YAAY,CAAC,IAAI,EAAE;QAAE,MAAM,EAAE,cAAc,EAAE,CAAA;KAAE,GAAG,IAAI;IAmBtD;;OAEG;IACH,eAAe,CAAC,KAAK,EAAE,MAAM,GAAG,cAAc,EAAE;IAIhD;;OAEG;IACH,YAAY,CAAC,KAAK,EAAE;QAClB,IAAI,CAAC,EAAE,MAAM,CAAC;QACd,MAAM,CAAC,EAAE,MAAM,CAAC;QAChB,SAAS,CAAC,EAAE,MAAM,CAAC;QACnB,UAAU,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;QACjC,IAAI,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAC9B,SAAS,CAAC,EAAE,IAAI,CAAC;QACjB,OAAO,CAAC,EAAE,IAAI,CAAC;KAChB,GAAG,cAAc,EAAE;CA8CrB"}