/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */
export interface RetryOptions {
    maxAttempts: number;
    baseDelayMs: number;
    maxDelayMs: number;
    backoffMultiplier: number;
    jitter: boolean;
    retryCondition?: (error: unknown) => boolean;
}
/**
 * Retry a function with exponential backoff
 */
export declare function retryWithBackoff<T>(fn: () => Promise<T>, options?: Partial<RetryOptions>): Promise<T>;
/**
 * Retry a function with a timeout
 */
export declare function retryWithTimeout<T>(fn: () => Promise<T>, timeoutMs: number, retryOptions?: Partial<RetryOptions>): Promise<T>;
/**
 * Sleep for a specified number of milliseconds
 */
export declare function sleep(ms: number): Promise<void>;
/**
 * Create a debounced version of a function
 */
export declare function debounce<T extends (...args: any[]) => any>(fn: T, delayMs: number): (...args: Parameters<T>) => Promise<ReturnType<T>>;
/**
 * Create a throttled version of a function
 */
export declare function throttle<T extends (...args: any[]) => any>(fn: T, delayMs: number): (...args: Parameters<T>) => ReturnType<T> | undefined;
