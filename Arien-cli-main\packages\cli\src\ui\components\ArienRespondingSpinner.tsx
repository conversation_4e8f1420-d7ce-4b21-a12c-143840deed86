/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import React from 'react';
import { Text } from 'ink';
import type { SpinnerName } from 'cli-spinners';
import { useStreamingContext } from '../contexts/StreamingContext.js';
import { StreamingState } from '../types.js';
import { AnimatedIcon } from './shared/AnimatedIcon.js';
import { Colors } from '../colors.js';

interface ArienRespondingSpinnerProps {
  /**
   * Optional string to display when not in Responding state.
   * If not provided and not Responding, renders null.
   */
  nonRespondingDisplay?: string;
  spinnerType?: SpinnerName; // Keep for backwards compatibility but not used
}

export const ArienRespondingSpinner: React.FC<
  ArienRespondingSpinnerProps
> = ({ nonRespondingDisplay }) => {
  const streamingState = useStreamingContext();

  if (streamingState === StreamingState.Responding) {
    return <AnimatedIcon isPending={true} color={Colors.AccentPurple} />;
  } else if (streamingState === StreamingState.WaitingForConfirmation) {
    return <Text color={Colors.AccentYellow}>{nonRespondingDisplay || '⚡'}</Text>;
  } else if (nonRespondingDisplay) {
    return <Text>{nonRespondingDisplay}</Text>;
  }
  return null;
}; 