/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */
import React from 'react';
import { Config, TelemetryManager, Logger } from '@arien/arien-ai-core';
import { CliArgs } from '../../utils/args.js';
export interface AuthCommandProps {
    config: Config;
    args: CliArgs;
    telemetry: TelemetryManager;
    logger: Logger;
    onExit: () => void;
}
export declare const AuthCommand: React.FC<AuthCommandProps>;
