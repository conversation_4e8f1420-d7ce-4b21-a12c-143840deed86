/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */

export interface CliArgs {
  // Commands
  command?: string;
  subcommand?: string;
  
  // Options
  help?: boolean;
  version?: boolean;
  debug?: boolean;
  config?: string;
  model?: string;
  approvalMode?: 'auto' | 'manual' | 'none';
  sandbox?: boolean;
  telemetry?: boolean;
  
  // Chat options
  message?: string;
  file?: string;
  
  // Positional arguments
  positional: string[];
  
  // Raw arguments
  raw: string[];
}

/**
 * Parse command line arguments
 */
export function parseArgs(argv: string[]): CliArgs {
  const args: CliArgs = {
    positional: [],
    raw: [...argv],
    telemetry: true, // Default to enabled
  };
  
  let i = 0;
  while (i < argv.length) {
    const arg = argv[i];
    
    // Handle flags
    if (arg && arg.startsWith('-')) {
      switch (arg) {
        case '-h':
        case '--help':
          args.help = true;
          break;
          
        case '-v':
        case '--version':
          args.version = true;
          break;
          
        case '-d':
        case '--debug':
          args.debug = true;
          break;
          
        case '-c':
        case '--config':
          i++;
          if (i < argv.length) {
            args.config = argv[i];
          } else {
            throw new Error('--config requires a value');
          }
          break;
          
        case '--model':
          i++;
          if (i < argv.length) {
            args.model = argv[i];
          } else {
            throw new Error('--model requires a value');
          }
          break;
          
        case '--approval-mode':
          i++;
          if (i < argv.length) {
            const mode = argv[i];
            if (mode === 'auto' || mode === 'manual' || mode === 'none') {
              args.approvalMode = mode;
            } else {
              throw new Error('--approval-mode must be auto, manual, or none');
            }
          } else {
            throw new Error('--approval-mode requires a value');
          }
          break;
          
        case '--sandbox':
          args.sandbox = true;
          break;
          
        case '--no-sandbox':
          args.sandbox = false;
          break;
          
        case '--telemetry':
          args.telemetry = true;
          break;
          
        case '--no-telemetry':
          args.telemetry = false;
          break;
          
        case '-m':
        case '--message':
          i++;
          if (i < argv.length) {
            args.message = argv[i];
          } else {
            throw new Error('--message requires a value');
          }
          break;
          
        case '-f':
        case '--file':
          i++;
          if (i < argv.length) {
            args.file = argv[i];
          } else {
            throw new Error('--file requires a value');
          }
          break;
          
        default:
          // Handle combined short flags like -dv
          if (arg && arg.startsWith('-') && !arg.startsWith('--') && arg.length > 2) {
            const flags = arg.slice(1);
            for (const flag of flags) {
              switch (flag) {
                case 'h':
                  args.help = true;
                  break;
                case 'v':
                  args.version = true;
                  break;
                case 'd':
                  args.debug = true;
                  break;
                default:
                  throw new Error(`Unknown flag: -${flag}`);
              }
            }
          } else {
            throw new Error(`Unknown option: ${arg}`);
          }
          break;
      }
    } else {
      // Handle positional arguments and commands
      if (arg) {
        if (!args.command) {
          args.command = arg;
        } else if (!args.subcommand && isValidSubcommand(args.command, arg)) {
          args.subcommand = arg;
        } else {
          args.positional.push(arg);
        }
      }
    }
    
    i++;
  }
  
  // Set default command if none provided
  if (!args.command && !args.help && !args.version) {
    args.command = 'chat';
  }
  
  return args;
}

/**
 * Check if a subcommand is valid for a given command
 */
function isValidSubcommand(command: string, subcommand: string): boolean {
  const validSubcommands: Record<string, string[]> = {
    config: ['show', 'set', 'get', 'list', 'reset'],
    tools: ['list', 'info', 'test'],
    memory: ['list', 'show', 'delete', 'clear', 'export', 'import'],
    auth: ['login', 'logout', 'status', 'refresh'],
  };
  
  return validSubcommands[command]?.includes(subcommand) || false;
}

/**
 * Validate parsed arguments
 */
export function validateArgs(args: CliArgs): void {
  // Validate command
  const validCommands = ['chat', 'config', 'tools', 'memory', 'auth', 'version', 'help'];
  if (args.command && !validCommands.includes(args.command)) {
    throw new Error(`Unknown command: ${args.command}`);
  }
  
  // Validate model if provided
  if (args.model) {
    const validModels = ['gemini-1.5-flash', 'gemini-1.5-pro', 'gemini-2.0-flash-exp'];
    if (!validModels.includes(args.model)) {
      console.warn(`Warning: Unknown model '${args.model}'. Valid models: ${validModels.join(', ')}`);
    }
  }
  
  // Validate file path if provided
  if (args.file) {
    try {
      // Basic path validation
      if (args.file.includes('\0')) {
        throw new Error('Invalid file path: contains null character');
      }
    } catch (error) {
      throw new Error(`Invalid file path: ${args.file}`);
    }
  }
}

/**
 * Convert args back to command line string (for logging/debugging)
 */
export function argsToString(args: CliArgs): string {
  const parts: string[] = [];
  
  if (args.command) parts.push(args.command);
  if (args.subcommand) parts.push(args.subcommand);
  if (args.debug) parts.push('--debug');
  if (args.config) parts.push('--config', args.config);
  if (args.model) parts.push('--model', args.model);
  if (args.approvalMode) parts.push('--approval-mode', args.approvalMode);
  if (args.sandbox === true) parts.push('--sandbox');
  if (args.sandbox === false) parts.push('--no-sandbox');
  if (args.telemetry === false) parts.push('--no-telemetry');
  if (args.message) parts.push('--message', args.message);
  if (args.file) parts.push('--file', args.file);
  
  parts.push(...args.positional);
  
  return parts.join(' ');
}

/**
 * Get help text for a specific command
 */
export function getCommandHelp(command: string): string {
  const helpTexts: Record<string, string> = {
    chat: `
Start an interactive chat session with the AI.

Usage: arien chat [options]

Options:
  -m, --message <text>    Send a single message and exit
  -f, --file <path>       Include file content in the conversation
  --model <model>         Use specific AI model
  --approval-mode <mode>  Set tool approval mode (auto|manual|none)
`,
    config: `
Manage ARIEN configuration.

Usage: arien config <subcommand> [options]

Subcommands:
  show                    Show current configuration
  set <key> <value>       Set configuration value
  get <key>               Get configuration value
  list                    List all configuration keys
  reset                   Reset configuration to defaults
`,
    tools: `
Manage and inspect available tools.

Usage: arien tools <subcommand> [options]

Subcommands:
  list                    List all available tools
  info <tool>             Show detailed information about a tool
  test <tool>             Test a tool with sample parameters
`,
    memory: `
Manage conversation memory and context.

Usage: arien memory <subcommand> [options]

Subcommands:
  list                    List stored memories
  show <id>               Show specific memory
  delete <id>             Delete a memory
  clear                   Clear all memories
  export <file>           Export memories to file
  import <file>           Import memories from file
`,
    auth: `
Manage authentication and credentials.

Usage: arien auth <subcommand> [options]

Subcommands:
  login                   Authenticate with Google
  logout                  Sign out and clear credentials
  status                  Show authentication status
  refresh                 Refresh authentication tokens
`,
  };
  
  return helpTexts[command] || `No help available for command: ${command}`;
}
