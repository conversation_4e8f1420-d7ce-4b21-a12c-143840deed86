import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */
import { useState, useEffect } from 'react';
import { Box, Text } from 'ink';
import { getToolRegistry } from '@arien/arien-ai-core';
import { useTheme } from '../ui/ThemeProvider.js';
import { LoadingSpinner } from '../ui/LoadingSpinner.js';
import { ErrorDisplay } from '../ui/ErrorDisplay.js';
export const ToolsCommand = ({ config, args, telemetry, logger, onExit, }) => {
    const theme = useTheme();
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState(null);
    const [result, setResult] = useState(null);
    useEffect(() => {
        const executeCommand = async () => {
            try {
                setIsLoading(true);
                telemetry.logEvent({
                    name: 'tools.command',
                    properties: {
                        subcommand: args.subcommand,
                        args: args.positional,
                    },
                });
                switch (args.subcommand) {
                    case 'list':
                        await handleList();
                        break;
                    case 'info':
                        await handleInfo();
                        break;
                    case 'test':
                        await handleTest();
                        break;
                    default:
                        await handleList(); // Default to list
                        break;
                }
            }
            catch (err) {
                const errorMessage = err instanceof Error ? err.message : String(err);
                logger.error('Tools command failed', { error: errorMessage }, 'ToolsCommand');
                setError(errorMessage);
            }
            finally {
                setIsLoading(false);
            }
        };
        executeCommand();
    }, [args.subcommand, args.positional, logger, telemetry]);
    const handleList = async () => {
        const registry = getToolRegistry();
        const tools = registry.getAllTools();
        if (tools.length === 0) {
            setResult('No tools available');
            return;
        }
        const toolList = tools.map((tool) => {
            const riskLevel = tool.riskLevel || 'unknown';
            const category = tool.category || 'general';
            return `  ${tool.name} (${category}, ${riskLevel} risk) - ${tool.description}`;
        }).join('\n');
        setResult(`Available Tools (${tools.length}):
${toolList}`);
    };
    const handleInfo = async () => {
        const [toolName] = args.positional;
        if (!toolName) {
            throw new Error('Usage: arien tools info <tool-name>');
        }
        const registry = getToolRegistry();
        const tool = registry.getTool(toolName);
        if (!tool) {
            throw new Error(`Tool not found: ${toolName}`);
        }
        const info = `Tool Information: ${tool.name}
Description: ${tool.description}
Category: ${tool.category || 'general'}
Risk Level: ${tool.riskLevel || 'unknown'}
Requires Approval: ${tool.requiresApproval ? 'Yes' : 'No'}

Parameters:
${tool.parameters ? JSON.stringify(tool.parameters, null, 2) : 'None'}

Examples:
${tool.examples?.map((example, i) => `  ${i + 1}. ${example.description}\n     ${JSON.stringify(example.parameters, null, 6)}`).join('\n') || 'None'}`;
        setResult(info);
    };
    const handleTest = async () => {
        const [toolName] = args.positional;
        if (!toolName) {
            throw new Error('Usage: arien tools test <tool-name>');
        }
        const registry = getToolRegistry();
        const tool = registry.getTool(toolName);
        if (!tool) {
            throw new Error(`Tool not found: ${toolName}`);
        }
        // TODO: Implement tool testing with sample parameters
        setResult(`Testing tool: ${toolName}
(Tool testing functionality would be implemented here)`);
    };
    // Auto-exit after showing result
    useEffect(() => {
        if (result && !isLoading) {
            const timer = setTimeout(() => {
                onExit();
            }, 5000);
            return () => clearTimeout(timer);
        }
        return () => { }; // Return empty cleanup function for other cases
    }, [result, isLoading, onExit]);
    if (isLoading) {
        return (_jsx(Box, { padding: 1, children: _jsx(LoadingSpinner, { text: "Loading tools..." }) }));
    }
    if (error) {
        return (_jsx(Box, { padding: 1, children: _jsx(ErrorDisplay, { error: error, onRetry: () => setError(null) }) }));
    }
    if (result) {
        return (_jsxs(Box, { flexDirection: "column", padding: 1, children: [_jsxs(Text, { color: theme.colors.info, children: [theme.symbols.info, " Tools"] }), _jsx(Box, { marginTop: 1, children: _jsx(Text, { children: result }) })] }));
    }
    return (_jsx(Box, { padding: 1, children: _jsx(Text, { color: theme.colors.textSecondary, children: "No result" }) }));
};
