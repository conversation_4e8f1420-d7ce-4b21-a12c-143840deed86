import { jsxs as _jsxs, jsx as _jsx } from "react/jsx-runtime";
import { Box, Text } from 'ink';
import { useTheme } from './ThemeProvider.js';
export const ErrorDisplay = ({ error, details, onRetry, showRetry = true, }) => {
    const theme = useTheme();
    return (_jsxs(Box, { flexDirection: "column", padding: 1, children: [_jsxs(Box, { children: [_jsxs(Text, { color: theme.colors.error, children: [theme.symbols.error, " "] }), _jsx(Text, { color: theme.colors.error, bold: true, children: "Error:" }), _jsxs(Text, { children: [" ", error] })] }), details && (_jsx(Box, { marginTop: 1, children: _jsxs(Text, { color: theme.colors.textSecondary, children: ["Details: ", details] }) })), showRetry && onRetry && (_jsx(Box, { marginTop: 1, children: _jsx(Text, { color: theme.colors.textSecondary, children: "Press any key to retry or Ctrl+C to exit" }) }))] }));
};
