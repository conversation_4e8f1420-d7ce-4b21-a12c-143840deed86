/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */
import { vi } from 'vitest';
// Mock environment variables for testing
process.env.NODE_ENV = 'test';
process.env.ARIEN_API_KEY = 'test-api-key';
process.env.ARIEN_SANDBOX = 'false';
// Mock console methods to reduce noise in tests
global.console = {
    ...console,
    log: vi.fn(),
    debug: vi.fn(),
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
};
// Mock process.exit to prevent tests from actually exiting
vi.spyOn(process, 'exit').mockImplementation(() => {
    throw new Error('process.exit() was called');
});
// Setup global test utilities
global.testUtils = {
    createMockConfig: () => ({
        getDebugMode: () => false,
        getApiKey: () => 'test-api-key',
        getModel: () => 'gemini-1.5-flash',
        getAuthType: () => 'api-key',
    }),
};
