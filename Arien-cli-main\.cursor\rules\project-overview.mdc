---
description: 
globs: 
alwaysApply: true
---
# Arien AI CLI - Project Overview

## Project Architecture

The Arien AI CLI is a command-line AI workflow tool with a modular architecture consisting of two main packages:

### Core Packages
- **CLI Package** ([packages/cli/](mdc:packages/cli)) - User-facing terminal interface built with React and Ink
- **Core Package** ([packages/core/](mdc:packages/core)) - Backend logic for AI interactions, tools, and state management

### Key Entry Points
- **Main CLI Entry**: [packages/cli/index.ts](mdc:packages/cli/index.ts) - CLI application entry point
- **Core Entry**: [packages/core/index.ts](mdc:packages/core/index.ts) - Core package exports
- **Main Arien App**: [packages/cli/src/arien.tsx](mdc:packages/cli/src/arien.tsx) - Primary CLI application logic
- **Bundle Entry**: [bundle/arien.js](mdc:bundle/arien.js) - Built distribution binary

### Project Structure
```
Arien-cli-main/
├── packages/
│   ├── cli/          # Terminal UI and user experience
│   └── core/         # AI backend and tools system
├── docs/             # Comprehensive documentation
├── integration-tests/ # End-to-end test suite
├── scripts/          # Build and utility scripts
└── bundle/           # Distribution artifacts
```

### Main Configuration Files
- **Root Package**: [package.json](mdc:package.json) - Main workspace configuration
- **TypeScript**: [tsconfig.json](mdc:tsconfig.json) - TypeScript project settings
- **Build System**: [esbuild.config.js](mdc:esbuild.config.js) - Build configuration
- **Linting**: [eslint.config.js](mdc:eslint.config.js) - Code quality rules

### Key Documentation
- **Architecture Guide**: [docs/architecture.md](mdc:docs/architecture.md) - System design overview
- **CLI Commands**: [docs/cli/commands.md](mdc:docs/cli/commands.md) - Available commands
- **Tools Documentation**: [docs/tools/](mdc:docs/tools) - Tool system reference
- **Main README**: [README.md](mdc:README.md) - Project introduction and quickstart

### Workflow
1. User input handled by CLI package
2. Request sent to Core package  
3. Core orchestrates AI API calls and tool execution
4. Results formatted and displayed by CLI package

This is a TypeScript monorepo using npm workspaces with comprehensive testing, documentation, and build automation.

