/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */
import { ToolExecutionMode, estimateToolRisk } from './tools.js';
import { ToolExecutionError } from '../utils/errors.js';
export class ToolRegistry {
    tools = new Map();
    stats = new Map();
    approvalHandler;
    executionMode = ToolExecutionMode.MANUAL;
    /**
     * Register a tool
     */
    register(tool) {
        this.tools.set(tool.name, tool);
        // Initialize stats
        if (!this.stats.has(tool.name)) {
            this.stats.set(tool.name, {
                toolName: tool.name,
                executionCount: 0,
                successCount: 0,
                errorCount: 0,
                averageExecutionTime: 0,
            });
        }
    }
    /**
     * Unregister a tool
     */
    unregister(toolName) {
        return this.tools.delete(toolName);
    }
    /**
     * Get a tool by name
     */
    getTool(name) {
        return this.tools.get(name);
    }
    /**
     * Get all registered tools
     */
    getAllTools() {
        return Array.from(this.tools.values());
    }
    /**
     * Get tools by category
     */
    getToolsByCategory(category) {
        return this.getAllTools().filter(tool => tool.category === category);
    }
    /**
     * Get tool definitions for AI model registration
     */
    getToolDefinitions() {
        return this.getAllTools().map(tool => {
            if ('getDefinition' in tool && typeof tool.getDefinition === 'function') {
                return tool.getDefinition();
            }
            // Fallback for tools that don't implement getDefinition
            return {
                name: tool.name,
                description: tool.description,
                parameters: {
                    type: 'object',
                    properties: {},
                },
            };
        });
    }
    /**
     * Set the execution mode
     */
    setExecutionMode(mode) {
        this.executionMode = mode;
    }
    /**
     * Set the approval handler
     */
    setApprovalHandler(handler) {
        this.approvalHandler = handler;
    }
    /**
     * Execute a tool
     */
    async executeTool(toolName, parameters, context) {
        const tool = this.getTool(toolName);
        if (!tool) {
            throw new ToolExecutionError(`Tool '${toolName}' not found`, toolName);
        }
        const startTime = Date.now();
        let result;
        try {
            // Check if approval is required
            if (this.executionMode === ToolExecutionMode.MANUAL &&
                (tool.requiresApproval || estimateToolRisk(toolName, parameters) !== 'low')) {
                if (!this.approvalHandler) {
                    throw new ToolExecutionError(`Tool '${toolName}' requires approval but no approval handler is set`, toolName);
                }
                const approvalRequest = {
                    toolName,
                    parameters,
                    description: tool.description,
                    context,
                    estimatedRisk: estimateToolRisk(toolName, parameters),
                };
                const approval = await this.approvalHandler(approvalRequest);
                if (!approval.approved) {
                    return {
                        success: false,
                        error: `Tool execution denied: ${approval.reason || 'No reason provided'}`,
                    };
                }
                // Use modified parameters if provided
                if (approval.modifiedParameters) {
                    parameters = approval.modifiedParameters;
                }
            }
            // Execute the tool
            result = await tool.execute(parameters, context);
            // Update stats
            this.updateStats(toolName, true, Date.now() - startTime);
        }
        catch (error) {
            // Update stats for error
            this.updateStats(toolName, false, Date.now() - startTime);
            if (error instanceof Error) {
                result = {
                    success: false,
                    error: error.message,
                };
            }
            else {
                result = {
                    success: false,
                    error: 'Unknown error occurred',
                };
            }
        }
        return result;
    }
    /**
     * Execute multiple tools in sequence
     */
    async executeTools(toolCalls, context) {
        const results = [];
        for (const call of toolCalls) {
            const result = await this.executeTool(call.name, call.parameters, context);
            results.push(result);
            // Stop execution if a tool fails and it's marked as critical
            if (!result.success && result.metadata?.critical) {
                break;
            }
        }
        return results;
    }
    /**
     * Get execution statistics for a tool
     */
    getToolStats(toolName) {
        return this.stats.get(toolName);
    }
    /**
     * Get execution statistics for all tools
     */
    getAllStats() {
        return Array.from(this.stats.values());
    }
    /**
     * Reset statistics for a tool
     */
    resetStats(toolName) {
        if (toolName) {
            const stats = this.stats.get(toolName);
            if (stats) {
                stats.executionCount = 0;
                stats.successCount = 0;
                stats.errorCount = 0;
                stats.averageExecutionTime = 0;
                stats.lastExecuted = undefined;
            }
        }
        else {
            // Reset all stats
            for (const stats of this.stats.values()) {
                stats.executionCount = 0;
                stats.successCount = 0;
                stats.errorCount = 0;
                stats.averageExecutionTime = 0;
                stats.lastExecuted = undefined;
            }
        }
    }
    /**
     * Update execution statistics
     */
    updateStats(toolName, success, executionTime) {
        const stats = this.stats.get(toolName);
        if (!stats)
            return;
        stats.executionCount++;
        stats.lastExecuted = new Date();
        if (success) {
            stats.successCount++;
        }
        else {
            stats.errorCount++;
        }
        // Update average execution time
        stats.averageExecutionTime =
            (stats.averageExecutionTime * (stats.executionCount - 1) + executionTime) /
                stats.executionCount;
    }
    /**
     * Check if a tool exists
     */
    hasTool(toolName) {
        return this.tools.has(toolName);
    }
    /**
     * Get tool count
     */
    getToolCount() {
        return this.tools.size;
    }
    /**
     * Clear all tools
     */
    clear() {
        this.tools.clear();
        this.stats.clear();
    }
}
// Global tool registry instance
let globalRegistry = null;
/**
 * Get the global tool registry
 */
export function getToolRegistry() {
    if (!globalRegistry) {
        globalRegistry = new ToolRegistry();
    }
    return globalRegistry;
}
/**
 * Set the global tool registry
 */
export function setToolRegistry(registry) {
    globalRegistry = registry;
}
//# sourceMappingURL=tool-registry.js.map