/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */
import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
/**
 * Build a specific module using TypeScript compiler
 */
async function buildModule() {
    const cwd = process.cwd();
    const moduleName = path.basename(cwd);
    console.log(`🔨 Building module: ${moduleName}`);
    // Clean dist directory first
    console.log('🧹 Cleaning dist directory...');
    const cleanProcess = spawn('npm', ['run', 'clean'], {
        stdio: 'inherit',
        shell: true,
        cwd,
    });
    await new Promise((resolve, reject) => {
        cleanProcess.on('close', (code) => {
            if (code === 0) {
                resolve();
            }
            else {
                reject(new Error(`Clean failed with code ${code}`));
            }
        });
    });
    // Run TypeScript compiler
    console.log('📦 Compiling TypeScript...');
    const tscProcess = spawn('npx', ['tsc', '--build'], {
        stdio: 'inherit',
        shell: true,
        cwd,
    });
    await new Promise((resolve, reject) => {
        tscProcess.on('close', (code) => {
            if (code === 0) {
                console.log(`✅ Module ${moduleName} built successfully`);
                resolve();
            }
            else {
                console.error(`❌ Module ${moduleName} build failed with code ${code}`);
                reject(new Error(`Build failed with code ${code}`));
            }
        });
    });
}
// Run the build
buildModule().catch((error) => {
    console.error('Build failed:', error);
    process.exit(1);
});
