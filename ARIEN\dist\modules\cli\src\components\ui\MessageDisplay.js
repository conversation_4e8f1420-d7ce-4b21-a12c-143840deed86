import { jsxs as _jsxs, jsx as _jsx } from "react/jsx-runtime";
import { Box, Text } from 'ink';
export const MessageDisplay = ({ message, theme, showTimestamp = false, showTokens = false, }) => {
    const isUser = message.role === 'user';
    const roleColor = isUser ? theme.colors.primary : theme.colors.success;
    const roleSymbol = isUser ? '>' : theme.symbols.arrow;
    const roleName = isUser ? 'You' : 'AI';
    return (_jsxs(Box, { flexDirection: "column", marginBottom: 1, children: [_jsxs(Box, { children: [_jsxs(Text, { color: roleColor, bold: true, children: [roleSymbol, " ", roleName] }), showTimestamp && (_jsxs(Text, { color: theme.colors.textSecondary, children: [' ', "(", message.timestamp.toLocaleTimeString(), ")"] })), showTokens && message.tokens && (_jsxs(Text, { color: theme.colors.textSecondary, children: [' ', "[", message.tokens, " tokens]"] }))] }), _jsx(Box, { paddingLeft: 2, children: _jsx(Text, { children: message.content }) })] }));
};
