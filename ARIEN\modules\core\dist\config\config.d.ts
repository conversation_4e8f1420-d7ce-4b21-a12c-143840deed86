/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */
export type AuthType = 'oauth' | 'api-key' | 'service-account';
export type ApprovalMode = 'auto' | 'manual' | 'none';
export interface EditTool {
    name: string;
    command: string;
    args?: string[];
}
export interface ShellTool {
    name: string;
    command: string;
    args?: string[];
}
export interface WriteFileTool {
    enabled: boolean;
    maxFileSize?: number;
}
export interface ConfigData {
    authType?: AuthType;
    apiKey?: string;
    serviceAccountPath?: string;
    model?: string;
    maxTokens?: number;
    temperature?: number;
    approvalMode?: ApprovalMode;
    editTool?: EditTool;
    shellTool?: ShellTool;
    writeFileTool?: WriteFileTool;
    sandboxEnabled?: boolean;
    sandboxImage?: string;
    debugMode?: boolean;
    telemetryEnabled?: boolean;
    theme?: string;
    colorMode?: 'auto' | 'light' | 'dark';
    memoryEnabled?: boolean;
    maxContextSize?: number;
    extensions?: string[];
    mcpServers?: Array<{
        name: string;
        command: string;
        args?: string[];
    }>;
}
export declare class Config {
    private data;
    private configPaths;
    constructor(configData?: ConfigData);
    private getConfigPaths;
    private loadConfig;
    private applyEnvironmentOverrides;
    getAuthType(): AuthType;
    getApiKey(): string | undefined;
    getServiceAccountPath(): string | undefined;
    getModel(): string;
    getMaxTokens(): number;
    getTemperature(): number;
    getApprovalMode(): ApprovalMode;
    getEditTool(): EditTool;
    getShellTool(): ShellTool;
    getWriteFileTool(): WriteFileTool;
    isSandboxEnabled(): boolean;
    getSandboxImage(): string;
    getDebugMode(): boolean;
    isTelemetryEnabled(): boolean;
    getTheme(): string;
    getColorMode(): 'auto' | 'light' | 'dark';
    isMemoryEnabled(): boolean;
    getMaxContextSize(): number;
    getExtensions(): string[];
    getMcpServers(): Array<{
        name: string;
        command: string;
        args?: string[];
    }>;
    updateConfig(updates: Partial<ConfigData>): void;
    getRawConfig(): ConfigData;
}
export declare function getConfig(): Config;
export declare function setConfig(config: Config): void;
export declare function loadCliConfig(configData?: ConfigData): Config;
//# sourceMappingURL=config.d.ts.map