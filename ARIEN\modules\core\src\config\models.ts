/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */

export interface ModelConfig {
  name: string;
  displayName: string;
  maxTokens: number;
  supportsTools: boolean;
  supportsVision: boolean;
  supportsCodeExecution: boolean;
  costPer1kTokens: {
    input: number;
    output: number;
  };
}

export const AVAILABLE_MODELS: Record<string, ModelConfig> = {
  'gemini-1.5-flash': {
    name: 'gemini-1.5-flash',
    displayName: 'Gemini 1.5 Flash',
    maxTokens: 1048576, // 1M tokens
    supportsTools: true,
    supportsVision: true,
    supportsCodeExecution: true,
    costPer1kTokens: {
      input: 0.075,
      output: 0.30,
    },
  },
  'gemini-1.5-pro': {
    name: 'gemini-1.5-pro',
    displayName: 'Gemini 1.5 Pro',
    maxTokens: 2097152, // 2M tokens
    supportsTools: true,
    supportsVision: true,
    supportsCodeExecution: true,
    costPer1kTokens: {
      input: 1.25,
      output: 5.00,
    },
  },
  'gemini-2.0-flash-exp': {
    name: 'gemini-2.0-flash-exp',
    displayName: '<PERSON> 2.0 Flash (Experimental)',
    maxTokens: 1048576, // 1M tokens
    supportsTools: true,
    supportsVision: true,
    supportsCodeExecution: true,
    costPer1kTokens: {
      input: 0.075,
      output: 0.30,
    },
  },
};

export const DEFAULT_ARIEN_FLASH_MODEL = 'gemini-1.5-flash';
export const DEFAULT_ARIEN_PRO_MODEL = 'gemini-1.5-pro';

export function getModelConfig(modelName: string): ModelConfig {
  const config = AVAILABLE_MODELS[modelName];
  if (!config) {
    throw new Error(`Unknown model: ${modelName}`);
  }
  return config;
}

export function isValidModel(modelName: string): boolean {
  return modelName in AVAILABLE_MODELS;
}

export function getAvailableModels(): ModelConfig[] {
  return Object.values(AVAILABLE_MODELS);
}

export function getDefaultModel(): string {
  return DEFAULT_ARIEN_FLASH_MODEL;
}

export function getBestModelForTask(task: {
  requiresVision?: boolean;
  requiresCodeExecution?: boolean;
  requiresLargeContext?: boolean;
  prioritizeCost?: boolean;
}): string {
  const models = getAvailableModels();
  
  // Filter models based on requirements
  let candidates = models.filter((model) => {
    if (task.requiresVision && !model.supportsVision) return false;
    if (task.requiresCodeExecution && !model.supportsCodeExecution) return false;
    return true;
  });
  
  if (candidates.length === 0) {
    return getDefaultModel();
  }
  
  // Sort by preference
  candidates.sort((a, b) => {
    // If large context is required, prefer models with higher token limits
    if (task.requiresLargeContext) {
      if (a.maxTokens !== b.maxTokens) {
        return b.maxTokens - a.maxTokens;
      }
    }
    
    // If cost is a priority, prefer cheaper models
    if (task.prioritizeCost) {
      const aCost = a.costPer1kTokens.input + a.costPer1kTokens.output;
      const bCost = b.costPer1kTokens.input + b.costPer1kTokens.output;
      if (aCost !== bCost) {
        return aCost - bCost;
      }
    }
    
    // Default preference: Flash > Pro > Experimental
    const preference = ['flash', 'pro', 'exp'];
    const aIndex = preference.findIndex((p) => a.name.includes(p));
    const bIndex = preference.findIndex((p) => b.name.includes(p));
    
    return aIndex - bIndex;
  });
  
  return candidates[0]?.name || getDefaultModel();
}
