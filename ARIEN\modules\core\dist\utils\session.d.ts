/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */
export declare const sessionId: `${string}-${string}-${string}-${string}-${string}`;
/**
 * Log a user prompt for telemetry and debugging purposes
 */
export declare function logUserPrompt(prompt: string): void;
/**
 * Get all logged user prompts for this session
 */
export declare function getUserPrompts(): Array<{
    timestamp: Date;
    prompt: string;
    sessionId: string;
}>;
/**
 * Clear all logged user prompts
 */
export declare function clearUserPrompts(): void;
/**
 * Get session statistics
 */
export declare function getSessionStats(): {
    sessionId: string;
    startTime: Date;
    promptCount: number;
    uptime: number;
};
//# sourceMappingURL=session.d.ts.map