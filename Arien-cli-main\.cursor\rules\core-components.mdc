---
description: 
globs: 
alwaysApply: true
---
# Core Package Components Guide

## AI Chat System

### Main Chat Logic
- **Arien Chat**: [packages/core/src/core/arienChat.ts](mdc:packages/core/src/core/arienChat.ts) - Primary AI conversation orchestrator
- **Prompts**: [packages/core/src/core/prompts.ts](mdc:packages/core/src/core/prompts.ts) - Prompt construction and management
- **Conversation**: [packages/core/src/core/conversation.ts](mdc:packages/core/src/core/conversation.ts) - Conversation state management

### AI Models & Configuration
- **Model Definitions**: [packages/core/src/config/models.ts](mdc:packages/core/src/config/models.ts) - AI model configurations
- **Core Config**: [packages/core/src/config/config.ts](mdc:packages/core/src/config/config.ts) - Core package settings
- **API Client**: Integration with Google Gemini API

## Tools System

### Tool Categories
- **File System Tools**: [packages/core/src/tools/fileSystemTools.ts](mdc:packages/core/src/tools/fileSystemTools.ts) - File operations
- **Shell Tools**: [packages/core/src/tools/shellTools.ts](mdc:packages/core/src/tools/shellTools.ts) - Command execution
- **Web Tools**: [packages/core/src/tools/webFetchTool.ts](mdc:packages/core/src/tools/webFetchTool.ts) - HTTP requests
- **Search Tools**: [packages/core/src/tools/webSearchTool.ts](mdc:packages/core/src/tools/webSearchTool.ts) - Web search capabilities

### Tool Framework
- **Tool Registry**: [packages/core/src/tools/](mdc:packages/core/src/tools) - Tool registration and execution
- **MCP Integration**: [packages/core/src/tools/mcpTools.ts](mdc:packages/core/src/tools/mcpTools.ts) - Model Context Protocol servers
- **Tool Validation**: Built-in safety and validation for tool execution

## Services & Utilities

### Core Services  
- **File Discovery**: [packages/core/src/services/fileDiscoveryService.ts](mdc:packages/core/src/services/fileDiscoveryService.ts) - Intelligent file finding
- **Memory Management**: [packages/core/src/tools/memoryTool.ts](mdc:packages/core/src/tools/memoryTool.ts) - Conversation memory

### Utility Functions
- **BFS File Search**: [packages/core/src/utils/bfsFileSearch.ts](mdc:packages/core/src/utils/bfsFileSearch.ts) - Breadth-first file searching
- **Diff Utilities**: [packages/core/src/utils/diffUtils.ts](mdc:packages/core/src/utils/diffUtils.ts) - File difference calculations
- **Path Utilities**: [packages/core/src/utils/pathUtils.ts](mdc:packages/core/src/utils/pathUtils.ts) - Path manipulation helpers

## Code Assistance

### Code Analysis
- **Code Assist**: [packages/core/src/code_assist/codeAssist.ts](mdc:packages/core/src/code_assist/codeAssist.ts) - Code understanding and analysis
- **AST Processing**: [packages/core/src/code_assist/astProcessor.ts](mdc:packages/core/src/code_assist/astProcessor.ts) - Abstract syntax tree analysis
- **Symbol Analysis**: Code symbol discovery and analysis

## Telemetry & Monitoring

### Telemetry System
- **Main Telemetry**: [packages/core/src/telemetry/](mdc:packages/core/src/telemetry) - Analytics and monitoring
- **OpenTelemetry**: [packages/core/src/telemetry/openTelemetry.ts](mdc:packages/core/src/telemetry/openTelemetry.ts) - Observability integration
- **Clearcut Logger**: [packages/core/src/telemetry/clearcut-logger/](mdc:packages/core/src/telemetry/clearcut-logger) - Google Analytics integration

### Performance Monitoring
- **Metrics Collection**: Performance and usage metrics
- **Error Tracking**: Error reporting and analysis
- **Privacy Controls**: Opt-in/opt-out telemetry controls

## State Management

### Conversation State
- **Session Management**: Multi-turn conversation handling
- **Context Window**: Large context window management (1M+ tokens)
- **Checkpointing**: [docs/checkpointing.md](mdc:docs/checkpointing.md) - Conversation state persistence

### Configuration Management
- **Environment Config**: Environment variable handling
- **User Preferences**: User setting persistence
- **Authentication**: [packages/core/src/config/auth.ts](mdc:packages/core/src/config/auth.ts) - Authentication management

