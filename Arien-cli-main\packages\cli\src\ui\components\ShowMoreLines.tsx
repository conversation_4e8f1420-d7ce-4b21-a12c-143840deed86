/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { Box, Text } from 'ink';
import { useOverflowState } from '../contexts/OverflowContext.js';
import { useStreamingContext } from '../contexts/StreamingContext.js';
import { StreamingState } from '../types.js';
import { Colors } from '../colors.js';

interface ShowMoreLinesProps {
  constrainHeight: boolean;
}

export const ShowMoreLines = ({ constrainHeight }: ShowMoreLinesProps) => {
  const overflowState = useOverflowState();
  const streamingState = useStreamingContext();

  if (
    overflowState === undefined ||
    overflowState.overflowingIds.size === 0 ||
    !constrainHeight ||
    !(
      streamingState === StreamingState.Idle ||
      streamingState === StreamingState.WaitingForConfirmation
    )
  ) {
    return null;
  }

  const overflowCount = overflowState.overflowingIds.size;
  
  return (
    <Box marginTop={0} flexDirection="row">
      <Text color={Colors.AccentYellow}>▼ </Text>
      <Text color={Colors.Gray} wrap="truncate">
        {overflowCount} item{overflowCount > 1 ? 's' : ''} hidden · Press 
      </Text>
      <Text color={Colors.AccentYellow} bold> Ctrl+S </Text>
      <Text color={Colors.Gray}>to show all</Text>
    </Box>
  );
};
