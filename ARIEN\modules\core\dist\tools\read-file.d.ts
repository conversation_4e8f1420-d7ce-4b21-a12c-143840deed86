/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */
import { z } from 'zod';
import { BaseTool, ToolContext, ToolResult } from './tools.js';
export declare class ReadFileTool extends BaseTool {
    name: string;
    description: string;
    parameters: z.ZodObject<{
        path: z.ZodString;
        encoding: z.ZodDefault<z.ZodOptional<z.ZodEnum<["utf8", "base64", "hex"]>>>;
        maxSize: z.ZodDefault<z.ZodOptional<z.ZodNumber>>;
        startLine: z.ZodOptional<z.ZodNumber>;
        endLine: z.ZodOptional<z.ZodNumber>;
    }, "strip", z.ZodType<PERSON>ny, {
        path: string;
        encoding: "utf8" | "base64" | "hex";
        maxSize: number;
        startLine?: number | undefined;
        endLine?: number | undefined;
    }, {
        path: string;
        encoding?: "utf8" | "base64" | "hex" | undefined;
        maxSize?: number | undefined;
        startLine?: number | undefined;
        endLine?: number | undefined;
    }>;
    category: "file-system";
    requiresApproval: boolean;
    examples: ({
        description: string;
        parameters: {
            path: string;
            startLine?: undefined;
            endLine?: undefined;
            encoding?: undefined;
        };
    } | {
        description: string;
        parameters: {
            path: string;
            startLine: number;
            endLine: number;
            encoding?: undefined;
        };
    } | {
        description: string;
        parameters: {
            path: string;
            encoding: string;
            startLine?: undefined;
            endLine?: undefined;
        };
    })[];
    execute(params: any, context: ToolContext): Promise<ToolResult>;
}
/**
 * Utility function to check if a file is likely to be text
 */
export declare function isTextFile(filePath: string): boolean;
/**
 * Utility function to get file info without reading content
 */
export declare function getFileInfo(filePath: string, workingDirectory: string): {
    exists: boolean;
    isFile?: boolean;
    isDirectory?: boolean;
    size?: number;
    lastModified?: Date;
    isText?: boolean;
};
//# sourceMappingURL=read-file.d.ts.map