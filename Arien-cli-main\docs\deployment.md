# Deployment

This document describes how to deploy or install Arien CLI in different environments.

## Web Development

For web development, you can install Arien CLI globally or use it via `npx`:

### Global installation

```bash
npm install -g @arien/arien-cli
```

### Usage with npx

```bash
npx @arien/arien-cli
```

You can also run the CLI directly from the repository without installing it:

```bash
npx https://github.com/arien-ai/arien-cli
```

## Docker

You can use Arien CLI's sandboxing feature, which runs tools in a Docker container:

```bash
docker run --rm -it us-docker.pkg.dev/arien-code-dev/arien-cli/sandbox:0.1.1
```

This uses a pre-built sandbox image that includes Arien CLI.

### Running from GitHub Repositories

You can run Arien CLI directly from GitHub repositories:

```bash
npx https://github.com/arien-ai/arien-cli
```

This will download and run the latest version from the repository.

## Development Environment

For development purposes, you can:

1. Clone the repository
2. Build the project
3. Run the CLI from the local build

Refer to the [contribution guide](./CONTRIBUTING.md) for detailed setup instructions.

### Container Sandboxing

The Docker-based execution method is supported by the `arien-cli-sandbox` container image. This image is published to a container registry and contains a pre-installed, global version of Arien CLI. The `scripts/prepare-cli-packagejson.js` script dynamically updates dependencies in the package.json file for production builds.

#### Building the Sandbox Image

To build your own sandbox image:

1.  Ensure you have Docker installed and running.
2.  Build the core and CLI packages using `npm run build:packages`.
3.  Build and tag the `arien-cli-sandbox` Docker image.

```bash
npm run build:sandbox
```
