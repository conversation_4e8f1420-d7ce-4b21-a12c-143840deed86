/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import React from 'react';
import { Box, Text } from 'ink';
import { Colors } from '../colors.js';

export const ShellModeIndicator: React.FC = () => (
  <Box borderStyle="round" borderColor={Colors.AccentYellow} paddingX={1}>
    <Text color={Colors.AccentYellow} bold>
      ! Shell Mode
    </Text>
    <Text color={Colors.Gray} dimColor> (ESC to exit)</Text>
  </Box>
);
