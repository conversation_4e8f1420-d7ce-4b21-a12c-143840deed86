/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */
import React from 'react';
import { Config, TelemetryManager, Logger } from '@arien/arien-ai-core';
import { CliArgs } from '../utils/args.js';
export interface ArienCLIProps {
    config: Config;
    args: CliArgs;
    telemetry: TelemetryManager;
    logger: Logger;
}
export declare const ArienCLI: React.FC<ArienCLIProps>;
