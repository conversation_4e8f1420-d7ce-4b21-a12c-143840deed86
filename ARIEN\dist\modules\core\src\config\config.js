/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */
import { readFileSync, existsSync } from 'fs';
import path from 'path';
import os from 'os';
import { env, cwd, platform } from 'process';
// Simple JSON comment stripper
function stripJsonComments(jsonString) {
    return jsonString.replace(/\/\*[\s\S]*?\*\/|\/\/.*$/gm, '');
}
export class Config {
    data;
    configPaths;
    constructor(configData) {
        this.configPaths = this.getConfigPaths();
        this.data = this.loadConfig(configData);
    }
    getConfigPaths() {
        const homeDir = os.homedir();
        const currentDir = cwd();
        return [
            path.join(currentDir, '.arien.json'),
            path.join(currentDir, '.arien', 'config.json'),
            path.join(homeDir, '.arien', 'config.json'),
            path.join(homeDir, '.config', 'arien', 'config.json'),
        ];
    }
    loadConfig(override) {
        let config = {};
        // Load from config files (in reverse order, so later ones override earlier ones)
        for (const configPath of this.configPaths.reverse()) {
            if (existsSync(configPath)) {
                try {
                    const content = readFileSync(configPath, 'utf8');
                    const fileConfig = JSON.parse(stripJsonComments(content));
                    config = { ...config, ...fileConfig };
                }
                catch (error) {
                    console.warn(`Warning: Failed to load config from ${configPath}:`, error);
                }
            }
        }
        // Apply environment variable overrides
        config = this.applyEnvironmentOverrides(config);
        // Apply any direct overrides
        if (override) {
            config = { ...config, ...override };
        }
        return config;
    }
    applyEnvironmentOverrides(config) {
        const envOverrides = {};
        // Authentication
        if (env.ARIEN_API_KEY) {
            envOverrides.apiKey = env.ARIEN_API_KEY;
            envOverrides.authType = 'api-key';
        }
        if (env.ARIEN_SERVICE_ACCOUNT_PATH) {
            envOverrides.serviceAccountPath = env.ARIEN_SERVICE_ACCOUNT_PATH;
            envOverrides.authType = 'service-account';
        }
        // Model configuration
        if (env.ARIEN_MODEL) {
            envOverrides.model = env.ARIEN_MODEL;
        }
        if (env.ARIEN_MAX_TOKENS) {
            envOverrides.maxTokens = parseInt(env.ARIEN_MAX_TOKENS, 10);
        }
        if (env.ARIEN_TEMPERATURE) {
            envOverrides.temperature = parseFloat(env.ARIEN_TEMPERATURE);
        }
        // Tool configuration
        if (env.ARIEN_APPROVAL_MODE) {
            envOverrides.approvalMode = env.ARIEN_APPROVAL_MODE;
        }
        // Sandbox
        if (env.ARIEN_SANDBOX !== undefined) {
            envOverrides.sandboxEnabled = env.ARIEN_SANDBOX === 'true';
        }
        if (env.ARIEN_SANDBOX_IMAGE) {
            envOverrides.sandboxImage = env.ARIEN_SANDBOX_IMAGE;
        }
        // Debug and logging
        if (env.DEBUG) {
            envOverrides.debugMode = env.DEBUG === '1' || env.DEBUG === 'true';
        }
        if (env.ARIEN_TELEMETRY !== undefined) {
            envOverrides.telemetryEnabled = env.ARIEN_TELEMETRY === 'true';
        }
        // UI
        if (env.ARIEN_THEME) {
            envOverrides.theme = env.ARIEN_THEME;
        }
        if (env.ARIEN_COLOR_MODE) {
            envOverrides.colorMode = env.ARIEN_COLOR_MODE;
        }
        return { ...config, ...envOverrides };
    }
    // Getters for configuration values with defaults
    getAuthType() {
        return this.data.authType || 'oauth';
    }
    getApiKey() {
        return this.data.apiKey;
    }
    getServiceAccountPath() {
        return this.data.serviceAccountPath;
    }
    getModel() {
        return this.data.model || 'gemini-1.5-flash';
    }
    getMaxTokens() {
        return this.data.maxTokens || 8192;
    }
    getTemperature() {
        return this.data.temperature || 0.7;
    }
    getApprovalMode() {
        return this.data.approvalMode || 'manual';
    }
    getEditTool() {
        return this.data.editTool || {
            name: 'default',
            command: env.EDITOR || 'nano',
        };
    }
    getShellTool() {
        return this.data.shellTool || {
            name: 'default',
            command: platform === 'win32' ? 'cmd' : 'bash',
        };
    }
    getWriteFileTool() {
        return this.data.writeFileTool || {
            enabled: true,
            maxFileSize: 1024 * 1024, // 1MB
        };
    }
    isSandboxEnabled() {
        return this.data.sandboxEnabled ?? false;
    }
    getSandboxImage() {
        return this.data.sandboxImage || 'us-docker.pkg.dev/arien-code-dev/arien-cli/sandbox:1.0.0';
    }
    getDebugMode() {
        return this.data.debugMode ?? false;
    }
    isTelemetryEnabled() {
        return this.data.telemetryEnabled ?? true;
    }
    getTheme() {
        return this.data.theme || 'default';
    }
    getColorMode() {
        return this.data.colorMode || 'auto';
    }
    isMemoryEnabled() {
        return this.data.memoryEnabled ?? true;
    }
    getMaxContextSize() {
        return this.data.maxContextSize || 1000000; // 1M tokens
    }
    getExtensions() {
        return this.data.extensions || [];
    }
    getMcpServers() {
        return this.data.mcpServers || [];
    }
    // Update configuration
    updateConfig(updates) {
        this.data = { ...this.data, ...updates };
    }
    // Get raw config data
    getRawConfig() {
        return { ...this.data };
    }
}
// Global config instance
let globalConfig = null;
export function getConfig() {
    if (!globalConfig) {
        globalConfig = new Config();
    }
    return globalConfig;
}
export function setConfig(config) {
    globalConfig = config;
}
export function loadCliConfig(configData) {
    const config = new Config(configData);
    setConfig(config);
    return config;
}
