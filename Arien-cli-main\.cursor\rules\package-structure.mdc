---
description: 
globs: 
alwaysApply: true
---
# Package Structure Guide

## CLI Package (`packages/cli/`)

### Core Structure
- **Entry Point**: [packages/cli/index.ts](mdc:packages/cli/index.ts) - Main CLI entry with error handling
- **Main App**: [packages/cli/src/arien.tsx](mdc:packages/cli/src/arien.tsx) - Primary application logic
- **Package Config**: [packages/cli/package.json](mdc:packages/cli/package.json) - CLI package dependencies

### Key Directories
- **UI Components**: [packages/cli/src/ui/components/](mdc:packages/cli/src/ui/components) - React components for terminal UI
- **Themes**: [packages/cli/src/ui/themes/](mdc:packages/cli/src/ui/themes) - Color themes and styling
- **Configuration**: [packages/cli/src/config/](mdc:packages/cli/src/config) - CLI settings and auth
- **Hooks**: [packages/cli/src/ui/hooks/](mdc:packages/cli/src/ui/hooks) - React hooks for CLI functionality
- **Utils**: [packages/cli/src/utils/](mdc:packages/cli/src/utils) - CLI utility functions

### Important Files
- **App Component**: [packages/cli/src/ui/App.tsx](mdc:packages/cli/src/ui/App.tsx) - Main React app component
- **Non-Interactive CLI**: [packages/cli/src/nonInteractiveCli.ts](mdc:packages/cli/src/nonInteractiveCli.ts) - Batch processing mode

## Core Package (`packages/core/`)

### Core Structure  
- **Entry Point**: [packages/core/index.ts](mdc:packages/core/index.ts) - Package exports
- **Main Index**: [packages/core/src/index.ts](mdc:packages/core/src/index.ts) - Core functionality exports
- **Package Config**: [packages/core/package.json](mdc:packages/core/package.json) - Core package dependencies

### Key Directories
- **Core Logic**: [packages/core/src/core/](mdc:packages/core/src/core) - AI chat and conversation management
- **Tools System**: [packages/core/src/tools/](mdc:packages/core/src/tools) - File system, shell, web tools
- **Configuration**: [packages/core/src/config/](mdc:packages/core/src/config) - Core settings and models
- **Services**: [packages/core/src/services/](mdc:packages/core/src/services) - File discovery and other services
- **Telemetry**: [packages/core/src/telemetry/](mdc:packages/core/src/telemetry) - Analytics and logging
- **Utils**: [packages/core/src/utils/](mdc:packages/core/src/utils) - Core utility functions
- **Code Assist**: [packages/core/src/code_assist/](mdc:packages/core/src/code_assist) - Code analysis features

### Critical Core Files
- **Arien Chat**: [packages/core/src/core/arienChat.ts](mdc:packages/core/src/core/arienChat.ts) - Main chat orchestration
- **Tool Registry**: [packages/core/src/tools/](mdc:packages/core/src/tools) - Tool registration and execution
- **Model Config**: [packages/core/src/config/models.ts](mdc:packages/core/src/config/models.ts) - AI model definitions

## Dependencies
- **CLI Dependencies**: React, Ink (terminal UI), yargs (CLI parsing)
- **Core Dependencies**: Google AI SDK, OpenTelemetry, MCP SDK, WebSocket
- **Shared**: TypeScript, Vitest (testing), ESLint (linting)

