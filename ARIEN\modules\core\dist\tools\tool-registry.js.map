{"version": 3, "file": "tool-registry.js", "sourceRoot": "", "sources": ["../../src/tools/tool-registry.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAOL,iBAAiB,EACjB,gBAAgB,EACjB,MAAM,YAAY,CAAC;AACpB,OAAO,EAAE,kBAAkB,EAAE,MAAM,oBAAoB,CAAC;AAExD,MAAM,OAAO,YAAY;IACf,KAAK,GAAG,IAAI,GAAG,EAA0B,CAAC;IAC1C,KAAK,GAAG,IAAI,GAAG,EAA8B,CAAC;IAC9C,eAAe,CAAmE;IAClF,aAAa,GAAsB,iBAAiB,CAAC,MAAM,CAAC;IAEpE;;OAEG;IACH,QAAQ,CAAC,IAAoB;QAC3B,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAEhC,mBAAmB;QACnB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YAC/B,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE;gBACxB,QAAQ,EAAE,IAAI,CAAC,IAAI;gBACnB,cAAc,EAAE,CAAC;gBACjB,YAAY,EAAE,CAAC;gBACf,UAAU,EAAE,CAAC;gBACb,oBAAoB,EAAE,CAAC;aACxB,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,QAAgB;QACzB,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,OAAO,CAAC,IAAY;QAClB,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,WAAW;QACT,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,QAAgB;QACjC,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC;IACvE,CAAC;IAED;;OAEG;IACH,kBAAkB;QAShB,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YACnC,IAAI,eAAe,IAAI,IAAI,IAAI,OAAO,IAAI,CAAC,aAAa,KAAK,UAAU,EAAE,CAAC;gBACxE,OAAO,IAAI,CAAC,aAAa,EAAE,CAAC;YAC9B,CAAC;YAED,wDAAwD;YACxD,OAAO;gBACL,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,UAAU,EAAE;oBACV,IAAI,EAAE,QAAiB;oBACvB,UAAU,EAAE,EAAE;iBACf;aACF,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAC,IAAuB;QACtC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,OAAwE;QACzF,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CACf,QAAgB,EAChB,UAAe,EACf,OAAoB;QAEpB,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACpC,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,MAAM,IAAI,kBAAkB,CAAC,SAAS,QAAQ,aAAa,EAAE,QAAQ,CAAC,CAAC;QACzE,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,IAAI,MAAkB,CAAC;QAEvB,IAAI,CAAC;YACH,gCAAgC;YAChC,IAAI,IAAI,CAAC,aAAa,KAAK,iBAAiB,CAAC,MAAM;gBAC/C,CAAC,IAAI,CAAC,gBAAgB,IAAI,gBAAgB,CAAC,QAAQ,EAAE,UAAU,CAAC,KAAK,KAAK,CAAC,EAAE,CAAC;gBAEhF,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;oBAC1B,MAAM,IAAI,kBAAkB,CAC1B,SAAS,QAAQ,oDAAoD,EACrE,QAAQ,CACT,CAAC;gBACJ,CAAC;gBAED,MAAM,eAAe,GAAwB;oBAC3C,QAAQ;oBACR,UAAU;oBACV,WAAW,EAAE,IAAI,CAAC,WAAW;oBAC7B,OAAO;oBACP,aAAa,EAAE,gBAAgB,CAAC,QAAQ,EAAE,UAAU,CAAC;iBACtD,CAAC;gBAEF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;gBAC7D,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;oBACvB,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE,0BAA0B,QAAQ,CAAC,MAAM,IAAI,oBAAoB,EAAE;qBAC3E,CAAC;gBACJ,CAAC;gBAED,sCAAsC;gBACtC,IAAI,QAAQ,CAAC,kBAAkB,EAAE,CAAC;oBAChC,UAAU,GAAG,QAAQ,CAAC,kBAAkB,CAAC;gBAC3C,CAAC;YACH,CAAC;YAED,mBAAmB;YACnB,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;YAEjD,eAAe;YACf,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,CAAC;QAE3D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,yBAAyB;YACzB,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,CAAC;YAE1D,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBAC3B,MAAM,GAAG;oBACP,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,KAAK,CAAC,OAAO;iBACrB,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,MAAM,GAAG;oBACP,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,wBAAwB;iBAChC,CAAC;YACJ,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CAChB,SAGE,EACF,OAAoB;QAEpB,MAAM,OAAO,GAAiB,EAAE,CAAC;QAEjC,KAAK,MAAM,IAAI,IAAI,SAAS,EAAE,CAAC;YAC7B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;YAC3E,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAErB,6DAA6D;YAC7D,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,QAAQ,EAAE,QAAQ,EAAE,CAAC;gBACjD,MAAM;YACR,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,QAAgB;QAC3B,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,WAAW;QACT,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,QAAiB;QAC1B,IAAI,QAAQ,EAAE,CAAC;YACb,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YACvC,IAAI,KAAK,EAAE,CAAC;gBACV,KAAK,CAAC,cAAc,GAAG,CAAC,CAAC;gBACzB,KAAK,CAAC,YAAY,GAAG,CAAC,CAAC;gBACvB,KAAK,CAAC,UAAU,GAAG,CAAC,CAAC;gBACrB,KAAK,CAAC,oBAAoB,GAAG,CAAC,CAAC;gBAC/B,KAAK,CAAC,YAAY,GAAG,SAAS,CAAC;YACjC,CAAC;QACH,CAAC;aAAM,CAAC;YACN,kBAAkB;YAClB,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC;gBACxC,KAAK,CAAC,cAAc,GAAG,CAAC,CAAC;gBACzB,KAAK,CAAC,YAAY,GAAG,CAAC,CAAC;gBACvB,KAAK,CAAC,UAAU,GAAG,CAAC,CAAC;gBACrB,KAAK,CAAC,oBAAoB,GAAG,CAAC,CAAC;gBAC/B,KAAK,CAAC,YAAY,GAAG,SAAS,CAAC;YACjC,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACK,WAAW,CAAC,QAAgB,EAAE,OAAgB,EAAE,aAAqB;QAC3E,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACvC,IAAI,CAAC,KAAK;YAAE,OAAO;QAEnB,KAAK,CAAC,cAAc,EAAE,CAAC;QACvB,KAAK,CAAC,YAAY,GAAG,IAAI,IAAI,EAAE,CAAC;QAEhC,IAAI,OAAO,EAAE,CAAC;YACZ,KAAK,CAAC,YAAY,EAAE,CAAC;QACvB,CAAC;aAAM,CAAC;YACN,KAAK,CAAC,UAAU,EAAE,CAAC;QACrB,CAAC;QAED,gCAAgC;QAChC,KAAK,CAAC,oBAAoB;YACxB,CAAC,KAAK,CAAC,oBAAoB,GAAG,CAAC,KAAK,CAAC,cAAc,GAAG,CAAC,CAAC,GAAG,aAAa,CAAC;gBACzE,KAAK,CAAC,cAAc,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,OAAO,CAAC,QAAgB;QACtB,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,YAAY;QACV,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,KAAK;QACH,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;QACnB,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;IACrB,CAAC;CACF;AAED,gCAAgC;AAChC,IAAI,cAAc,GAAwB,IAAI,CAAC;AAE/C;;GAEG;AACH,MAAM,UAAU,eAAe;IAC7B,IAAI,CAAC,cAAc,EAAE,CAAC;QACpB,cAAc,GAAG,IAAI,YAAY,EAAE,CAAC;IACtC,CAAC;IACD,OAAO,cAAc,CAAC;AACxB,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,eAAe,CAAC,QAAsB;IACpD,cAAc,GAAG,QAAQ,CAAC;AAC5B,CAAC"}