/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */
import { z } from 'zod';
export interface ToolContext {
    workingDirectory: string;
    config: any;
    sessionId: string;
    userId?: string;
}
export interface ToolResult {
    success: boolean;
    data?: any;
    error?: string;
    metadata?: Record<string, any>;
}
export interface ToolDefinition {
    name: string;
    description: string;
    parameters: z.ZodSchema;
    execute: (params: any, context: ToolContext) => Promise<ToolResult>;
    requiresApproval?: boolean;
    category?: string;
    examples?: Array<{
        description: string;
        parameters: any;
        expectedResult?: any;
    }>;
}
export declare abstract class BaseTool implements ToolDefinition {
    abstract name: string;
    abstract description: string;
    abstract parameters: z.ZodSchema;
    abstract execute(params: any, context: ToolContext): Promise<ToolResult>;
    requiresApproval: boolean;
    category: string;
    examples: Array<{
        description: string;
        parameters: any;
        expectedResult?: any;
    }>;
    /**
     * Validate parameters against the schema
     */
    protected validateParams(params: any): any;
    /**
     * Create a successful result
     */
    protected success(data?: any, metadata?: Record<string, any>): ToolResult;
    /**
     * Create an error result
     */
    protected error(message: string, metadata?: Record<string, any>): ToolResult;
    /**
     * Get the tool definition for AI model registration
     */
    getDefinition(): {
        name: string;
        description: string;
        parameters: {
            type: 'object';
            properties: Record<string, any>;
            required?: string[];
        };
    };
    /**
     * Convert Zod schema to JSON Schema for AI model
     */
    private zodToJsonSchema;
}
/**
 * Tool execution modes
 */
export declare enum ToolExecutionMode {
    AUTO = "auto",// Execute automatically without approval
    MANUAL = "manual",// Require manual approval before execution
    NONE = "none"
}
/**
 * Tool approval request
 */
export interface ToolApprovalRequest {
    toolName: string;
    parameters: any;
    description: string;
    context: ToolContext;
    estimatedRisk: 'low' | 'medium' | 'high';
    preview?: string;
}
/**
 * Tool approval response
 */
export interface ToolApprovalResponse {
    approved: boolean;
    reason?: string;
    modifiedParameters?: any;
}
/**
 * Tool execution statistics
 */
export interface ToolExecutionStats {
    toolName: string;
    executionCount: number;
    successCount: number;
    errorCount: number;
    averageExecutionTime: number;
    lastExecuted?: Date;
}
/**
 * Tool categories for organization
 */
export declare const TOOL_CATEGORIES: {
    readonly FILE_SYSTEM: "file-system";
    readonly SHELL: "shell";
    readonly WEB: "web";
    readonly MEMORY: "memory";
    readonly DEVELOPMENT: "development";
    readonly COMMUNICATION: "communication";
    readonly UTILITY: "utility";
};
export type ToolCategory = typeof TOOL_CATEGORIES[keyof typeof TOOL_CATEGORIES];
/**
 * Risk levels for tool execution
 */
export declare const RISK_LEVELS: {
    readonly LOW: "low";
    readonly MEDIUM: "medium";
    readonly HIGH: "high";
};
export type RiskLevel = typeof RISK_LEVELS[keyof typeof RISK_LEVELS];
/**
 * Utility function to estimate tool risk based on operation type
 */
export declare function estimateToolRisk(toolName: string, parameters: any): RiskLevel;
//# sourceMappingURL=tools.d.ts.map