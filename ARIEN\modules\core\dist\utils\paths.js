/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */
import path from 'path';
import os from 'os';
import { existsSync, mkdirSync } from 'fs';
/**
 * Get the ARIEN configuration directory
 */
export function getArienConfigDir() {
    const homeDir = os.homedir();
    return path.join(homeDir, '.arien');
}
/**
 * Get the ARIEN cache directory
 */
export function getArienCacheDir() {
    const configDir = getArienConfigDir();
    return path.join(configDir, 'cache');
}
/**
 * Get the ARIEN logs directory
 */
export function getArienLogsDir() {
    const configDir = getArienConfigDir();
    return path.join(configDir, 'logs');
}
/**
 * Get the ARIEN memory directory
 */
export function getArienMemoryDir() {
    const configDir = getArienConfigDir();
    return path.join(configDir, 'memory');
}
/**
 * Get the ARIEN extensions directory
 */
export function getArienExtensionsDir() {
    const configDir = getArienConfigDir();
    return path.join(configDir, 'extensions');
}
/**
 * Ensure a directory exists, creating it if necessary
 */
export function ensureDir(dirPath) {
    if (!existsSync(dirPath)) {
        mkdirSync(dirPath, { recursive: true });
    }
}
/**
 * Ensure all ARIEN directories exist
 */
export function ensureArienDirs() {
    ensureDir(getArienConfigDir());
    ensureDir(getArienCacheDir());
    ensureDir(getArienLogsDir());
    ensureDir(getArienMemoryDir());
    ensureDir(getArienExtensionsDir());
}
/**
 * Resolve a path relative to the current working directory
 */
export function resolvePath(filePath, workingDir) {
    const baseDir = workingDir || process.cwd();
    return path.resolve(baseDir, filePath);
}
/**
 * Check if a path is within a given directory (security check)
 */
export function isPathWithinDirectory(filePath, directory) {
    const resolvedPath = path.resolve(filePath);
    const resolvedDir = path.resolve(directory);
    return resolvedPath.startsWith(resolvedDir + path.sep) || resolvedPath === resolvedDir;
}
/**
 * Get a relative path from one directory to another
 */
export function getRelativePath(from, to) {
    return path.relative(from, to);
}
/**
 * Normalize a file path for cross-platform compatibility
 */
export function normalizePath(filePath) {
    return path.normalize(filePath).replace(/\\/g, '/');
}
/**
 * Get the file extension from a path
 */
export function getFileExtension(filePath) {
    return path.extname(filePath).toLowerCase();
}
/**
 * Get the filename without extension
 */
export function getBaseName(filePath) {
    return path.basename(filePath, path.extname(filePath));
}
/**
 * Join path segments safely
 */
export function joinPaths(...segments) {
    return path.join(...segments);
}
/**
 * Check if a path is absolute
 */
export function isAbsolutePath(filePath) {
    return path.isAbsolute(filePath);
}
/**
 * Convert a Windows path to Unix-style path
 */
export function toUnixPath(filePath) {
    return filePath.replace(/\\/g, '/');
}
/**
 * Convert a Unix-style path to Windows path
 */
export function toWindowsPath(filePath) {
    return filePath.replace(/\//g, '\\');
}
/**
 * Get the platform-appropriate path separator
 */
export function getPathSeparator() {
    return path.sep;
}
/**
 * Split a path into its components
 */
export function splitPath(filePath) {
    return filePath.split(path.sep).filter(Boolean);
}
/**
 * Get the common base path of multiple paths
 */
export function getCommonBasePath(paths) {
    if (paths.length === 0)
        return '';
    if (paths.length === 1)
        return path.dirname(paths[0]);
    const resolvedPaths = paths.map(p => path.resolve(p));
    const splitPaths = resolvedPaths.map(p => splitPath(p));
    let commonLength = 0;
    const minLength = Math.min(...splitPaths.map(p => p.length));
    for (let i = 0; i < minLength; i++) {
        const segment = splitPaths[0][i];
        if (splitPaths.every(p => p[i] === segment)) {
            commonLength++;
        }
        else {
            break;
        }
    }
    if (commonLength === 0)
        return '';
    return path.sep + splitPaths[0].slice(0, commonLength).join(path.sep);
}
/**
 * Check if a file path matches a glob pattern (simple implementation)
 */
export function matchesGlob(filePath, pattern) {
    // Convert glob pattern to regex
    const regexPattern = pattern
        .replace(/\./g, '\\.')
        .replace(/\*/g, '.*')
        .replace(/\?/g, '.');
    const regex = new RegExp(`^${regexPattern}$`, 'i');
    return regex.test(filePath);
}
/**
 * Get a temporary file path
 */
export function getTempPath(prefix = 'arien', suffix = '') {
    const tempDir = os.tmpdir();
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2);
    return path.join(tempDir, `${prefix}-${timestamp}-${random}${suffix}`);
}
//# sourceMappingURL=paths.js.map