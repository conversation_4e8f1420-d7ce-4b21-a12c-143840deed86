/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */
import { getToolRegistry } from './tool-registry.js';
import { ReadFileTool } from './read-file.js';
import { WriteFileTool } from './write-file.js';
import { ShellExecuteTool } from './shell-execute.js';
/**
 * Initialize and register all available tools
 */
export function initializeTools() {
    const registry = getToolRegistry();
    // Register core tools
    registry.register(new ReadFileTool());
    registry.register(new WriteFileTool());
    registry.register(new ShellExecuteTool());
    // TODO: Register additional tools as they are implemented
    // registry.register(new ListDirectoryTool());
    // registry.register(new SearchFilesTool());
    // registry.register(new GitTool());
    // registry.register(new WebSearchTool());
    // registry.register(new WebFetchTool());
}
/**
 * Get the global tool registry with all tools initialized
 */
export function getGlobalToolRegistry() {
    const registry = getToolRegistry();
    // Initialize tools if not already done
    if (registry.getToolCount() === 0) {
        initializeTools();
    }
    return registry;
}
//# sourceMappingURL=initialize-tools.js.map