/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */
import { z } from 'zod';
import { BaseTool, ToolContext, ToolResult } from './tools.js';
export declare class ShellExecuteTool extends BaseTool {
    name: string;
    description: string;
    parameters: z.ZodObject<{
        command: z.ZodString;
        args: z.ZodOptional<z.ZodA<PERSON>y<z.ZodString, "many">>;
        cwd: z.ZodOptional<z.ZodString>;
        timeout: z.ZodDefault<z.ZodOptional<z.ZodNumber>>;
        env: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodString>>;
        shell: z.ZodDefault<z.ZodOptional<z.ZodBoolean>>;
        captureOutput: z.ZodDefault<z.ZodOptional<z.ZodBoolean>>;
        interactive: z.ZodDefault<z.ZodOptional<z.ZodBoolean>>;
    }, "strip", z.<PERSON>, {
        command: string;
        shell: boolean;
        timeout: number;
        captureOutput: boolean;
        interactive: boolean;
        env?: Record<string, string> | undefined;
        args?: string[] | undefined;
        cwd?: string | undefined;
    }, {
        command: string;
        env?: Record<string, string> | undefined;
        args?: string[] | undefined;
        shell?: boolean | undefined;
        cwd?: string | undefined;
        timeout?: number | undefined;
        captureOutput?: boolean | undefined;
        interactive?: boolean | undefined;
    }>;
    category: "shell";
    requiresApproval: boolean;
    examples: ({
        description: string;
        parameters: {
            command: string;
            timeout?: undefined;
            cwd?: undefined;
            env?: undefined;
        };
    } | {
        description: string;
        parameters: {
            command: string;
            timeout: number;
            cwd: string;
            env?: undefined;
        };
    } | {
        description: string;
        parameters: {
            command: string;
            env: {
                CUSTOM_VAR: string;
            };
            timeout?: undefined;
            cwd?: undefined;
        };
    })[];
    execute(params: any, context: ToolContext): Promise<ToolResult>;
    private executeWithExec;
    private executeWithSpawn;
    private executeInteractive;
    getRiskLevel(): 'low' | 'medium' | 'high';
}
