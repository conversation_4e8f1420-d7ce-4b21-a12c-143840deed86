/**
 * @license
 * Copyright 2025 ARIEN AI
 * SPDX-License-Identifier: Apache-2.0
 */
import { getLogger } from '@arien/arien-ai-core';
/**
 * Setup global error handling for the CLI
 */
export function setupErrorHandling() {
    const logger = getLogger();
    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
        logger.error('Unhandled Promise Rejection', {
            reason: reason instanceof Error ? reason.message : String(reason),
            stack: reason instanceof Error ? reason.stack : undefined,
            promise: promise.toString(),
        });
        console.error('Unhandled Promise Rejection:', reason);
        process.exit(1);
    });
    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
        logger.error('Uncaught Exception', {
            message: error.message,
            stack: error.stack,
            name: error.name,
        });
        console.error('Uncaught Exception:', error);
        process.exit(1);
    });
    // Handle SIGINT (Ctrl+C)
    process.on('SIGINT', () => {
        logger.info('Received SIGINT, shutting down gracefully');
        console.log('\nShutting down gracefully...');
        process.exit(0);
    });
    // Handle SIGTERM
    process.on('SIGTERM', () => {
        logger.info('Received SIGTERM, shutting down gracefully');
        console.log('\nShutting down gracefully...');
        process.exit(0);
    });
}
/**
 * Format error for display to user
 */
export function formatError(error) {
    if (error instanceof Error) {
        return `Error: ${error.message}`;
    }
    if (typeof error === 'string') {
        return `Error: ${error}`;
    }
    return `Error: ${String(error)}`;
}
/**
 * Format error with stack trace for debugging
 */
export function formatErrorWithStack(error) {
    if (error instanceof Error) {
        return `${error.name}: ${error.message}\n${error.stack || 'No stack trace available'}`;
    }
    return formatError(error);
}
/**
 * Check if error is a user-facing error that should be displayed nicely
 */
export function isUserError(error) {
    if (error instanceof Error) {
        // Check for specific error types that are user-facing
        const userErrorTypes = [
            'ConfigurationError',
            'AuthenticationError',
            'ValidationError',
            'FileSystemError',
        ];
        return userErrorTypes.includes(error.constructor.name);
    }
    return false;
}
/**
 * Handle CLI command errors
 */
export function handleCommandError(error, command) {
    const logger = getLogger();
    logger.error('Command error', {
        command,
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
    });
    if (isUserError(error)) {
        console.error(formatError(error));
    }
    else {
        console.error('An unexpected error occurred. Please check the logs for more details.');
        console.error(formatError(error));
    }
}
/**
 * Create a safe async wrapper that handles errors
 */
export function safeAsync(fn) {
    return async (...args) => {
        try {
            return await fn(...args);
        }
        catch (error) {
            handleCommandError(error);
            return undefined;
        }
    };
}
/**
 * Create a safe sync wrapper that handles errors
 */
export function safeSync(fn) {
    return (...args) => {
        try {
            return fn(...args);
        }
        catch (error) {
            handleCommandError(error);
            return undefined;
        }
    };
}
/**
 * Exit codes for different error types
 */
export const EXIT_CODES = {
    SUCCESS: 0,
    GENERAL_ERROR: 1,
    INVALID_USAGE: 2,
    CONFIGURATION_ERROR: 3,
    AUTHENTICATION_ERROR: 4,
    NETWORK_ERROR: 5,
    FILE_ERROR: 6,
    PERMISSION_ERROR: 7,
    TIMEOUT_ERROR: 8,
};
/**
 * Get appropriate exit code for an error
 */
export function getExitCode(error) {
    if (error instanceof Error) {
        switch (error.constructor.name) {
            case 'ConfigurationError':
                return EXIT_CODES.CONFIGURATION_ERROR;
            case 'AuthenticationError':
                return EXIT_CODES.AUTHENTICATION_ERROR;
            case 'NetworkError':
                return EXIT_CODES.NETWORK_ERROR;
            case 'FileSystemError':
                return EXIT_CODES.FILE_ERROR;
            case 'ValidationError':
                return EXIT_CODES.INVALID_USAGE;
            case 'TimeoutError':
                return EXIT_CODES.TIMEOUT_ERROR;
            default:
                return EXIT_CODES.GENERAL_ERROR;
        }
    }
    return EXIT_CODES.GENERAL_ERROR;
}
/**
 * Exit with appropriate code and message
 */
export function exitWithError(error, command) {
    handleCommandError(error, command);
    const exitCode = getExitCode(error);
    process.exit(exitCode);
}
